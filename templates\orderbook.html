{% extends "base.html" %}

{% block head %}
<style>
    .stat-card {
        @apply bg-base-100 rounded-lg shadow-lg p-6 transition-all duration-300;
    }
    
    .stat-card:hover {
        @apply shadow-xl transform -translate-y-1;
    }

    .table-container {
        @apply overflow-x-auto bg-base-100 rounded-lg shadow-lg;
    }

    .table-header {
        @apply sticky top-0 bg-base-200 z-10;
    }

    .numeric-cell {
        @apply font-mono text-right;
    }
</style>
{% endblock %}

{% block content %}
<div class="w-full">
    <!-- Header Section -->
    <div class="mb-6">
        <h1 class="text-3xl font-bold">Order Book</h1>
        <p class="text-base-content/60">Track and manage your trading orders</p>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <!-- Buy Orders -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Buy Orders</div>
                <div class="stat-value text-success">{{ order_stats.total_buy_orders }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge badge-success">Long Positions</div>
                </div>
            </div>
        </div>

        <!-- Sell Orders -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Sell Orders</div>
                <div class="stat-value text-error">{{ order_stats.total_sell_orders }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge badge-error">Short Positions</div>
                </div>
            </div>
        </div>

        <!-- Completed Orders -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Completed Orders</div>
                <div class="stat-value text-success">{{ order_stats.total_completed_orders }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge badge-success">Executed</div>
                </div>
            </div>
        </div>

        <!-- Open Orders -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Open Orders</div>
                <div class="stat-value text-warning">{{ order_stats.total_open_orders }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge badge-warning">Pending</div>
                </div>
            </div>
        </div>

        <!-- Rejected Orders -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Rejected Orders</div>
                <div class="stat-value text-error">{{ order_stats.total_rejected_orders }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge badge-error">Failed</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div id="orderbook-table-container" class="table-container">
        <table class="table w-full">
            <thead class="table-header">
                <tr>
                    <th class="cursor-pointer hover:bg-base-300">
                        Trading Symbol
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                        </svg>
                    </th>
                    <th>Exchange</th>
                    <th>Transaction Type</th>
                    <th>Quantity</th>
                    <th>Price</th>
                    <th>Trigger Price</th>
                    <th>Order Type</th>
                    <th>Product Type</th>
                    <th>Order ID</th>
                    <th>Status</th>
                    <th>Time</th>
                </tr>
            </thead>
            <tbody>
                {% for order in order_data %}
                <tr class="hover:bg-base-200">
                    <td class="font-medium">{{ order.symbol }}</td>
                    <td>
                        {% set exchange_colors = {
                            'NSE': 'badge-accent',
                            'BSE': 'badge-neutral',
                            'NFO': 'badge-secondary',
                            'MCX': 'badge-primary'
                        } %}
                        <div class="badge {{ exchange_colors.get(order.exchange, 'badge-ghost') }}">
                            {{ order.exchange }}
                        </div>
                    </td>
                    <td>
                        <div class="badge {% if order.action == 'BUY' %}badge-success{% else %}badge-error{% endif %} gap-2">
                            {% if order.action == 'BUY' %}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                            {% else %}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                            {% endif %}
                            {{ order.action }}
                        </div>
                    </td>
                    <td class="numeric-cell">{{ order.quantity }}</td>
                    <td class="numeric-cell">{{ order.price }}</td>
                    <td class="numeric-cell">{{ order.trigger_price }}</td>
                    <td>
                        {% set order_type_colors = {
                            'MARKET': 'badge-primary',
                            'LIMIT': 'badge-info',
                            'SL': 'badge-warning',
                            'SL-M': 'badge-warning'
                        } %}
                        <div class="badge {{ order_type_colors.get(order.pricetype, 'badge-ghost') }}">
                            {{ order.pricetype }}
                        </div>
                    </td>
                    <td>
                        {% set product_type_colors = {
                            'CNC': 'badge-secondary',
                            'MIS': 'badge-accent',
                            'NRML': 'badge-neutral'
                        } %}
                        <div class="badge {{ product_type_colors.get(order.product, 'badge-ghost') }}">
                            {{ order.product }}
                        </div>
                    </td>
                    <td class="font-mono text-sm">{{ order.orderid }}</td>
                    <td>
                        {% set status_colors = {
                            'complete': 'badge-success',
                            'rejected': 'badge-error',
                            'cancelled': 'badge-error',
                            'open': 'badge-warning',
                            'pending': 'badge-warning',
                            'trigger pending': 'badge-info'
                        } %}
                        <div class="badge {{ status_colors.get(order.order_status|lower, 'badge-ghost') }}">
                            {{ order.order_status }}
                        </div>
                    </td>
                    <td class="text-sm">{{ order.timestamp }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add sorting functionality to table headers
    document.querySelectorAll('th.cursor-pointer').forEach(headerCell => {
        headerCell.addEventListener('click', () => {
            const table = headerCell.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const index = Array.from(headerCell.parentElement.children).indexOf(headerCell);
            
            // Get current sort direction
            const currentDirection = headerCell.getAttribute('data-sort') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            
            // Update sort direction
            headerCell.setAttribute('data-sort', newDirection);
            
            // Sort rows
            rows.sort((a, b) => {
                const aValue = a.children[index].textContent.trim();
                const bValue = b.children[index].textContent.trim();
                
                // Check if values are numbers
                const aNum = parseFloat(aValue);
                const bNum = parseFloat(bValue);
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return newDirection === 'asc' ? aNum - bNum : bNum - aNum;
                }
                
                return newDirection === 'asc' 
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            });
            
            // Clear and repopulate tbody
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));
        });
    });
});
</script>
{% endblock %}
