# Configuration file for Price Based Options Strategy

# Strategy Configuration
STRATEGY_NAME = "Price Based Options Strategy"
UNDERLYING_SYMBOL = "NIFTY"  # Underlying symbol for options
EXCHANGE = "NFO"
PRODUCT = "MIS"
DEFAULT_LOTS = 1  # Default number of lots

# Strategy Parameters
PRICE_THRESHOLD = 10  # Points above/below POSP to trigger trades
STOP_LOSS_POINTS = 10  # Stop loss in points
TRAILING_SL_POINTS = 10  # Trailing stop loss points

# Market Hours (IST)
MARKET_START_TIME = "09:15"
MARKET_END_TIME = "15:30"

# Trading Parameters
TICK_INTERVAL = 1  # Seconds between price checks
MAX_RETRIES = 3  # Maximum retries for API calls
RETRY_DELAY = 5  # Seconds to wait between retries

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
LOG_INTERVAL = 10  # Log status every N iterations

# Risk Management
MAX_DAILY_TRADES = 50  # Maximum trades per day
MAX_DAILY_LOSS = 5000  # Maximum loss per day in INR
MAX_CONCURRENT_POSITIONS = 1  # Maximum concurrent positions

# Option Selection
OPTION_SELECTION_METHOD = "ATM"  # ATM, ITM, OTM
STRIKE_ROUNDING = 50  # Round strikes to nearest multiple (50 for NIFTY)

# Expiry Selection
EXPIRY_SELECTION = "CURRENT_MONTH"  # CURRENT_MONTH, NEXT_MONTH, WEEKLY
EXPIRY_DAY_THRESHOLD = 25  # If current day > this, use next month expiry

# API Configuration
API_HOST = "http://127.0.0.1:5000"
API_KEY = "your-openalgo-api-key"  # Replace with your actual API key

# Notification Settings
ENABLE_NOTIFICATIONS = False
NOTIFICATION_EMAIL = ""
NOTIFICATION_WEBHOOK = ""

# Performance Tracking
TRACK_PERFORMANCE = True
PERFORMANCE_LOG_FILE = "strategy_performance.log"

# Debug Settings
DEBUG_MODE = False
SAVE_TRADE_DATA = True
TRADE_DATA_FILE = "trade_data.csv" 