# Price Based Options Strategy

## Overview

This strategy implements a price-based options selling strategy that monitors ATM option price movements and executes option trades based on predefined price thresholds. The strategy is designed for intraday trading with automatic position management and risk controls.

## Strategy Logic

### Core Concept
1. **POSP (Point of Start Price)**: The strategy starts by recording the current ATM CE and PE option prices as reference points
2. **Entry Conditions**:
   - **CE Sell**: When CE option price goes 10 points below CE POSP → Sell ATM Call Option
   - **PE Sell**: When PE option price goes 10 points below PE POSP → Sell ATM Put Option
3. **Stop Loss**: 10 points from entry price
4. **Trailing Stop Loss**: Moves in favor as option price moves favorably (10 points trailing)
5. **New POSP**: After stop loss hit, re-initialize POSP with current ATM option prices

### Example Scenarios

#### Example 1: CE Sell
- **Start**: CE POSP = 45.50, PE POSP = 42.30 (ATM options at current strike)
- **CE price drops to 35.50**: Immediately sell CE option (10 points below CE POSP)
- **Stop Loss**: 45.50 (10 points above entry)
- **Trailing**: As CE price moves down, SL moves down by 10 points
- **Exit**: When SL is hit, close position and set new POSP

#### Example 2: PE Sell
- **Start**: CE POSP = 45.50, PE POSP = 42.30 (ATM options at current strike)
- **PE price drops to 32.30**: Immediately sell PE option (10 points below PE POSP)
- **Stop Loss**: 42.30 (10 points above entry)
- **Trailing**: As PE price moves down, SL moves down by 10 points
- **Exit**: When SL is hit, close position and set new POSP

## Features

### ✅ Implemented Features
- **Real-time option price monitoring** (1-second intervals)
- **Dynamic ATM option selection** based on current NIFTY price
- **Separate POSP tracking** for CE and PE options
- **Automatic trade execution** based on option price thresholds
- **Trailing stop loss** management
- **Risk management** with daily limits
- **Position tracking** and management
- **Trade data logging** to CSV file
- **Market hours** validation
- **Error handling** and retries
- **Configurable parameters**

### 🔧 Configuration Options
- **Lot size**: Configurable number of lots (default: 1)
- **Price threshold**: Points below option POSP (default: 10)
- **Stop loss**: Points from entry (default: 10)
- **Trailing SL**: Trailing points (default: 10)
- **Market hours**: Trading hours (default: 09:15-15:30)
- **Risk limits**: Daily trade and loss limits
- **Expiry selection**: Current/next month options

## Installation & Setup

### Prerequisites
1. **OpenAlgo Setup**: Ensure OpenAlgo is properly configured with your broker (Upstox)
2. **API Key**: Get your OpenAlgo API key from the portal
3. **Python Dependencies**: Install required packages

### Setup Steps

1. **Update Configuration**:
   ```bash
   # Edit the configuration file
   nano strategies/price_based_options_config.py
   ```

2. **Update API Key**:
   ```python
   # In price_based_options_config.py
   API_KEY = "your-actual-openalgo-api-key"
   ```

3. **Adjust Parameters** (if needed):
   ```python
   # Strategy parameters
   PRICE_THRESHOLD = 10  # Points below option POSP
   STOP_LOSS_POINTS = 10  # Stop loss in points
   TRAILING_SL_POINTS = 10  # Trailing stop loss points
   DEFAULT_LOTS = 1  # Number of lots
   ```

## Usage

### Running the Strategy

1. **Navigate to strategies directory**:
   ```bash
   cd strategies
   ```

2. **Run the strategy**:
   ```bash
   python price_based_options_strategy.py
   ```

3. **Enter lot size** when prompted:
   ```
   Enter number of lots (default 1): 2
   ```

### Strategy Output

The strategy will display:
- **Initialization**: Strategy parameters and configuration
- **POSP Setup**: ATM option prices for CE and PE
- **Real-time logs**: Trade signals, executions, and status updates
- **Performance tracking**: Daily trades, P&L, and risk metrics
- **Error handling**: API errors, retries, and recovery

### Sample Output
```
=== Price Based Options Strategy ===
Underlying: NIFTY
Price Threshold: 10 points
Stop Loss: 10 points
Trailing SL: 10 points
Market Hours: 09:15 - 15:30

Enter number of lots (default 1): 1
2024-01-15 09:15:00 - INFO - Strategy initialized with 1 lots
2024-01-15 09:15:01 - INFO - Starting Price Based Options Strategy...
2024-01-15 09:15:02 - INFO - Initialized POSP - CE: NIFTY15JAN19250CE at 45.50, PE: NIFTY15JAN19250PE at 42.30
2024-01-15 09:15:30 - INFO - CE SELL signal executed: NIFTY15JAN19250CE at 35.50, SL: 45.50, Price diff: -10.0
2024-01-15 09:16:15 - INFO - Updated trailing SL for CE: 35.50
2024-01-15 09:17:00 - INFO - Stop loss hit for CE at 45.50
2024-01-15 09:17:01 - INFO - Re-initialized POSP with current ATM prices
```

## Configuration Details

### Strategy Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `PRICE_THRESHOLD` | 10 | Points below option POSP to trigger trades |
| `STOP_LOSS_POINTS` | 10 | Stop loss in points from entry |
| `TRAILING_SL_POINTS` | 10 | Trailing stop loss points |
| `DEFAULT_LOTS` | 1 | Default number of lots |
| `TICK_INTERVAL` | 1 | Seconds between price checks |

### Risk Management

| Parameter | Default | Description |
|-----------|---------|-------------|
| `MAX_DAILY_TRADES` | 50 | Maximum trades per day |
| `MAX_DAILY_LOSS` | 5000 | Maximum loss per day (INR) |
| `MAX_CONCURRENT_POSITIONS` | 1 | Maximum concurrent positions |

### Market Hours

| Parameter | Default | Description |
|-----------|---------|-------------|
| `MARKET_START_TIME` | "09:15" | Market opening time (IST) |
| `MARKET_END_TIME` | "15:30" | Market closing time (IST) |

## Strategy Logic Details

### POSP Initialization
- **ATM Strike Calculation**: Based on current NIFTY price, rounded to nearest 50
- **CE POSP**: Current price of ATM Call option
- **PE POSP**: Current price of ATM Put option
- **Dynamic Updates**: POSP updates when ATM strike changes

### Entry Conditions
- **CE Sell**: When CE option price ≤ (CE POSP - 10 points)
- **PE Sell**: When PE option price ≤ (PE POSP - 10 points)
- **Position Check**: Only execute if no current position

### Stop Loss Management
- **Initial SL**: Entry price + 10 points
- **Trailing SL**: Moves down as option price moves favorably
- **Exit**: Close position when current price ≥ Stop Loss

### Strike Change Handling
- **Detection**: Monitor if ATM strike changes
- **POSP Update**: Re-initialize POSP with new ATM option prices
- **Continuity**: Maintain strategy logic across strike changes

## Risk Management

### Built-in Protections
1. **Daily Trade Limits**: Maximum trades per day
2. **Daily Loss Limits**: Maximum loss per day
3. **Position Limits**: Maximum concurrent positions
4. **Market Hours**: Only trades during market hours
5. **Auto-close**: Closes positions at market close
6. **Error Handling**: Retries and recovery mechanisms

### Risk Considerations
- **Options Risk**: Selling options has unlimited risk
- **Market Risk**: Strategy performance depends on option price movements
- **Liquidity Risk**: ATM options may have liquidity issues
- **Technical Risk**: API failures, network issues, etc.

## Monitoring & Logging

### Log Files
- **Strategy Logs**: Real-time strategy execution logs
- **Trade Data**: CSV file with all trade details
- **Performance Logs**: Strategy performance metrics

### Trade Data Format
```csv
timestamp,action,symbol,quantity,entry_price,stop_loss,ce_posp,pe_posp,nifty_price,price_diff
2024-01-15 09:15:30,SELL,NIFTY15JAN19250CE,1,35.50,45.50,45.50,42.30,19250.0,-10.0
```

## Troubleshooting

### Common Issues

1. **API Key Error**:
   - Ensure API key is correctly set in config file
   - Verify OpenAlgo connection

2. **Option Price Data Issues**:
   - Check internet connection
   - Verify option symbol availability
   - Check market hours

3. **Order Execution Errors**:
   - Verify broker connection
   - Check account balance
   - Ensure sufficient margin

4. **Strategy Not Starting**:
   - Check Python dependencies
   - Verify file permissions
   - Check configuration file

### Debug Mode
Enable debug mode in config:
```python
DEBUG_MODE = True
```

## Performance Tracking

### Metrics Tracked
- **Total Trades**: Number of trades executed
- **Daily Trades**: Trades per day
- **Win Rate**: Percentage of profitable trades
- **P&L**: Profit and loss tracking
- **Risk Metrics**: Maximum drawdown, Sharpe ratio

### Performance Analysis
The strategy saves trade data to `trade_data.csv` for analysis:
- Trade timing and execution
- Entry and exit prices
- Stop loss levels
- Option price movements

## Testing

### Run Test Suite
```bash
python test_price_based_options.py
```

### Test Coverage
- POSP initialization with ATM options
- ATM strike calculation
- Option symbol generation
- CE/PE sell signal logic
- Trailing stop loss
- Stop loss checking
- Risk limits
- Strike change detection

## Disclaimer

⚠️ **Important**: This strategy is for educational purposes only. Options trading involves significant risk and may not be suitable for all investors. Please:

1. **Test thoroughly** in paper trading mode first
2. **Understand the risks** involved in options trading
3. **Start with small positions** and gradually increase
4. **Monitor performance** closely
5. **Have proper risk management** in place

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the configuration options
3. Test with paper trading first
4. Monitor logs for error messages

## Version History

- **v1.0**: Initial implementation with basic functionality
- **v1.1**: Added configuration file and risk management
- **v1.2**: Enhanced error handling and logging
- **v1.3**: Added performance tracking and trade data logging
- **v2.0**: **CORRECTED** - POSP now based on ATM option prices, not spot NIFTY 