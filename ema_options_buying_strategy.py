#!/usr/bin/env python3
"""
EMA Options Buying Strategy
===========================

A trend-following options buying strategy based on:
- Triple EMA system (9, 21, 34)
- MACD confluence (9, 13)
- ITM/ATM option selection
- Dynamic stop loss and trailing stops

Author: OpenAlgo Strategy
Version: 1.0
"""

import pandas as pd
import numpy as np
import requests
import time
import logging
from datetime import datetime, timedelta
from openalgo import api
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ema_options_strategy.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EMAOptionsStrategy:
    def __init__(self, api_key, timeframe="1m", underlying="NIFTY", lot_size=1):
        """
        Initialize EMA Options Buying Strategy
        
        Args:
            api_key: OpenAlgo API key
            timeframe: Trading timeframe (1m, 2m, 3m, 5m)
            underlying: NIFTY or BANKNIFTY
            lot_size: Number of lots to trade
        """
        self.api_key = api_key
        self.timeframe = timeframe
        self.underlying = underlying.upper()
        self.lot_size = lot_size
        
        # Initialize OpenAlgo client
        self.client = api(api_key=self.api_key, host='http://127.0.0.1:5000')
        
        # Strategy parameters
        self.ema_periods = [9, 21, 34]
        self.macd_fast = 9
        self.macd_slow = 13
        self.stop_loss_points = 15
        self.trailing_trigger = 10
        
        # Strategy state
        self.current_position = None
        self.entry_price = None
        self.stop_loss = None
        self.swing_high = None
        self.swing_low = None
        self.trailing_active = False
        
        # Data storage
        self.price_data = pd.DataFrame()
        self.last_signal = None
        self.last_macd_signal = None
        
        logger.info(f"🚀 EMA Options Strategy initialized")
        logger.info(f"   Underlying: {self.underlying}")
        logger.info(f"   Timeframe: {self.timeframe}")
        logger.info(f"   Lot Size: {self.lot_size}")

    def get_nifty_data(self, periods=100):
        """Get NIFTY historical data for analysis"""
        try:
            symbol_map = {
                "NIFTY": "NIFTY 50",
                "BANKNIFTY": "NIFTY BANK"
            }
            
            symbol = symbol_map.get(self.underlying, self.underlying)
            
            # Calculate date range
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=5)).strftime("%Y-%m-%d")
            
            response = requests.post(
                "http://127.0.0.1:5000/api/v1/history",
                json={
                    "apikey": self.api_key,
                    "symbol": symbol,
                    "exchange": "NSE",
                    "interval": self.timeframe,
                    "start_date": start_date,
                    "end_date": end_date
                },
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    df = pd.DataFrame(data['data'])
                    if not df.empty:
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                        df = df.sort_values('timestamp').tail(periods).reset_index(drop=True)
                        logger.info(f"📊 Retrieved {len(df)} candles for {symbol}")
                        return df
            
            logger.error(f"Failed to get historical data: {response.status_code}")
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"Error getting NIFTY data: {e}")
            return pd.DataFrame()

    def calculate_ema(self, data, period):
        """Calculate Exponential Moving Average"""
        return data.ewm(span=period, adjust=False).mean()

    def calculate_macd(self, data, fast=9, slow=13):
        """Calculate MACD with custom periods"""
        ema_fast = self.calculate_ema(data, fast)
        ema_slow = self.calculate_ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = self.calculate_ema(macd_line, 9)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram

    def analyze_ema_trend(self, df):
        """Analyze EMA trend and alignment"""
        if len(df) < max(self.ema_periods):
            return None, False
        
        # Calculate EMAs
        df['ema_9'] = self.calculate_ema(df['close'], 9)
        df['ema_21'] = self.calculate_ema(df['close'], 21)
        df['ema_34'] = self.calculate_ema(df['close'], 34)
        
        # Get latest values
        latest = df.iloc[-1]
        prev = df.iloc[-2] if len(df) > 1 else latest
        
        ema_9 = latest['ema_9']
        ema_21 = latest['ema_21']
        ema_34 = latest['ema_34']
        
        # Check EMA alignment and slope
        bullish_alignment = ema_9 > ema_21 > ema_34
        bearish_alignment = ema_9 < ema_21 < ema_34
        
        # Check if EMAs are sloping in the right direction
        ema_9_slope = ema_9 > prev['ema_9']
        ema_21_slope = ema_21 > prev['ema_21']
        ema_34_slope = ema_34 > prev['ema_34']
        
        bullish_slope = ema_9_slope and ema_21_slope and ema_34_slope
        bearish_slope = not ema_9_slope and not ema_21_slope and not ema_34_slope
        
        if bullish_alignment and bullish_slope:
            return "BULLISH", True
        elif bearish_alignment and bearish_slope:
            return "BEARISH", True
        else:
            return "NEUTRAL", False

    def analyze_macd_signal(self, df):
        """Analyze MACD crossover signals"""
        if len(df) < 20:
            return None
        
        macd_line, signal_line, histogram = self.calculate_macd(df['close'], self.macd_fast, self.macd_slow)
        
        df['macd'] = macd_line
        df['macd_signal'] = signal_line
        df['macd_histogram'] = histogram
        
        # Check for crossovers
        current_macd = macd_line.iloc[-1]
        current_signal = signal_line.iloc[-1]
        prev_macd = macd_line.iloc[-2]
        prev_signal = signal_line.iloc[-2]
        
        # Bullish crossover: MACD crosses above signal line
        if prev_macd <= prev_signal and current_macd > current_signal:
            return "BULLISH_CROSS"
        
        # Bearish crossover: MACD crosses below signal line
        elif prev_macd >= prev_signal and current_macd < current_signal:
            return "BEARISH_CROSS"
        
        return None

    def find_swing_levels(self, df, lookback=10):
        """Find swing high and low levels for stop loss"""
        if len(df) < lookback * 2:
            return None, None
        
        highs = df['high'].rolling(window=lookback, center=True).max()
        lows = df['low'].rolling(window=lookback, center=True).min()
        
        # Find recent swing high and low
        recent_data = df.tail(lookback * 2)
        swing_high = recent_data['high'].max()
        swing_low = recent_data['low'].min()
        
        return swing_high, swing_low

    def get_current_spot_price(self):
        """Get current NIFTY spot price"""
        try:
            symbol_map = {
                "NIFTY": "NIFTY 50",
                "BANKNIFTY": "NIFTY BANK"
            }

            symbol = symbol_map.get(self.underlying, self.underlying)

            response = requests.post(
                "http://127.0.0.1:5000/api/v1/quotes",
                json={
                    "apikey": self.api_key,
                    "symbol": symbol,
                    "exchange": "NSE"
                },
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    return float(data.get('data', {}).get('ltp', 0))

            logger.warning("Could not get live spot price, using fallback")
            return 24500 if self.underlying == "NIFTY" else 51000  # Fallback values

        except Exception as e:
            logger.error(f"Error getting spot price: {e}")
            return 24500 if self.underlying == "NIFTY" else 51000

    def select_option_strike(self, spot_price, option_type="CE", selection="ATM"):
        """Select appropriate option strike (ITM/ATM)"""
        try:
            # Round to nearest strike
            strike_interval = 50 if self.underlying == "NIFTY" else 100
            base_strike = round(spot_price / strike_interval) * strike_interval

            if selection == "ATM":
                strike = base_strike
            elif selection == "ITM":
                if option_type == "CE":
                    strike = base_strike - strike_interval  # ITM CE
                else:
                    strike = base_strike + strike_interval  # ITM PE
            else:
                strike = base_strike

            # Get current expiry
            expiry = self.get_current_expiry()
            symbol = f"{self.underlying}{expiry}{int(strike)}{option_type}"

            logger.info(f"🎯 Selected {selection} {option_type}: {symbol} (Strike: {strike})")
            return symbol, strike

        except Exception as e:
            logger.error(f"Error selecting option strike: {e}")
            return None, None

    def get_current_expiry(self):
        """Get current week expiry in DDMMMYY format"""
        today = datetime.now()

        # Find next Thursday (weekly expiry)
        days_ahead = 3 - today.weekday()  # Thursday is 3
        if days_ahead <= 0:
            days_ahead += 7

        expiry_date = today + timedelta(days=days_ahead)

        # Format as DDMMMYY (e.g., 14AUG24)
        month_map = {
            1: 'JAN', 2: 'FEB', 3: 'MAR', 4: 'APR', 5: 'MAY', 6: 'JUN',
            7: 'JUL', 8: 'AUG', 9: 'SEP', 10: 'OCT', 11: 'NOV', 12: 'DEC'
        }

        day = expiry_date.strftime('%d')
        month = month_map[expiry_date.month]
        year = expiry_date.strftime('%y')

        return f"{day}{month}{year}"

    def get_option_price(self, symbol):
        """Get current option price"""
        try:
            response = requests.post(
                "http://127.0.0.1:5000/api/v1/quotes",
                json={
                    "apikey": self.api_key,
                    "symbol": symbol,
                    "exchange": "NFO"
                },
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    return float(data.get('data', {}).get('ltp', 0))

            logger.warning(f"Could not get price for {symbol}")
            return None

        except Exception as e:
            logger.error(f"Error getting option price for {symbol}: {e}")
            return None

    def place_buy_order(self, symbol, quantity):
        """Place buy order for options"""
        try:
            response = self.client.placesmartorder(
                strategy="EMA_OPTIONS_BUY",
                symbol=symbol,
                action="BUY",
                exchange="NFO",
                price_type="MARKET",
                product="MIS",
                quantity=quantity,
                position_size=self.lot_size
            )

            logger.info(f"📈 BUY Order placed: {symbol} x {quantity}")
            logger.info(f"Response: {response}")
            return response

        except Exception as e:
            logger.error(f"Error placing buy order: {e}")
            return None

    def place_sell_order(self, symbol, quantity):
        """Place sell order to exit position"""
        try:
            response = self.client.placesmartorder(
                strategy="EMA_OPTIONS_BUY",
                symbol=symbol,
                action="SELL",
                exchange="NFO",
                price_type="MARKET",
                product="MIS",
                quantity=quantity,
                position_size=0
            )

            logger.info(f"📉 SELL Order placed: {symbol} x {quantity}")
            logger.info(f"Response: {response}")
            return response

        except Exception as e:
            logger.error(f"Error placing sell order: {e}")
            return None

    def check_entry_signal(self):
        """Check for entry signals based on EMA and MACD"""
        try:
            # Get latest data
            df = self.get_nifty_data(periods=100)
            if df.empty:
                return None, None

            # Analyze EMA trend
            ema_trend, ema_valid = self.analyze_ema_trend(df)
            if not ema_valid:
                return None, None

            # Analyze MACD signal
            macd_signal = self.analyze_macd_signal(df)
            if not macd_signal:
                return None, None

            # Check for confluence
            if ema_trend == "BULLISH" and macd_signal == "BULLISH_CROSS":
                logger.info("🟢 BULLISH Signal: EMA aligned + MACD bullish crossover")
                return "BUY_CE", df
            elif ema_trend == "BEARISH" and macd_signal == "BEARISH_CROSS":
                logger.info("🔴 BEARISH Signal: EMA aligned + MACD bearish crossover")
                return "BUY_PE", df

            return None, df

        except Exception as e:
            logger.error(f"Error checking entry signal: {e}")
            return None, None

    def calculate_stop_loss(self, option_type, swing_high, swing_low, current_price):
        """Calculate stop loss based on swing levels"""
        try:
            if option_type == "CE":
                # For CE, use swing low minus stop loss points
                if swing_low:
                    sl_level = swing_low - self.stop_loss_points
                    # Convert to option price equivalent (approximate)
                    sl_price = max(current_price * 0.3, current_price - (current_price * 0.7))
                else:
                    sl_price = current_price * 0.3  # 70% loss limit
            else:  # PE
                # For PE, use swing high plus stop loss points
                if swing_high:
                    sl_level = swing_high + self.stop_loss_points
                    # Convert to option price equivalent (approximate)
                    sl_price = max(current_price * 0.3, current_price - (current_price * 0.7))
                else:
                    sl_price = current_price * 0.3  # 70% loss limit

            logger.info(f"🛡️ Stop Loss calculated: {sl_price:.2f}")
            return sl_price

        except Exception as e:
            logger.error(f"Error calculating stop loss: {e}")
            return current_price * 0.3

    def update_trailing_stop(self, current_price):
        """Update trailing stop loss"""
        try:
            if not self.trailing_active:
                # Check if we should activate trailing
                profit = current_price - self.entry_price
                if profit >= self.trailing_trigger:
                    self.trailing_active = True
                    logger.info(f"🔄 Trailing stop activated at {self.trailing_trigger} points profit")

            if self.trailing_active:
                # Update trailing stop
                new_stop = current_price - self.stop_loss_points
                if new_stop > self.stop_loss:
                    self.stop_loss = new_stop
                    logger.info(f"📈 Trailing stop updated to: {self.stop_loss:.2f}")

        except Exception as e:
            logger.error(f"Error updating trailing stop: {e}")

    def check_exit_conditions(self, current_price):
        """Check if we should exit the position"""
        try:
            if not self.current_position:
                return False

            # Check stop loss
            if current_price <= self.stop_loss:
                logger.info(f"🛑 Stop Loss hit: {current_price:.2f} <= {self.stop_loss:.2f}")
                return True

            # Update trailing stop
            self.update_trailing_stop(current_price)

            return False

        except Exception as e:
            logger.error(f"Error checking exit conditions: {e}")
            return False

    def execute_entry(self, signal, df):
        """Execute entry based on signal"""
        try:
            if self.current_position:
                logger.info("Already in position, skipping entry")
                return

            # Get current spot price
            spot_price = self.get_current_spot_price()

            # Select option
            if signal == "BUY_CE":
                option_symbol, strike = self.select_option_strike(spot_price, "CE", "ATM")
            else:  # BUY_PE
                option_symbol, strike = self.select_option_strike(spot_price, "PE", "ATM")

            if not option_symbol:
                logger.error("Could not select option symbol")
                return

            # Get option price
            option_price = self.get_option_price(option_symbol)
            if not option_price:
                logger.error(f"Could not get price for {option_symbol}")
                return

            # Calculate quantity (lot size * multiplier)
            lot_multiplier = 50 if self.underlying == "NIFTY" else 25
            quantity = self.lot_size * lot_multiplier

            # Place order
            order_response = self.place_buy_order(option_symbol, quantity)
            if order_response:
                # Update position
                self.current_position = {
                    'symbol': option_symbol,
                    'type': signal,
                    'quantity': quantity,
                    'entry_time': datetime.now()
                }
                self.entry_price = option_price

                # Calculate stop loss
                swing_high, swing_low = self.find_swing_levels(df)
                self.swing_high = swing_high
                self.swing_low = swing_low
                self.stop_loss = self.calculate_stop_loss(
                    "CE" if signal == "BUY_CE" else "PE",
                    swing_high, swing_low, option_price
                )
                self.trailing_active = False

                logger.info(f"✅ Position opened: {option_symbol}")
                logger.info(f"   Entry Price: {option_price:.2f}")
                logger.info(f"   Stop Loss: {self.stop_loss:.2f}")
                logger.info(f"   Quantity: {quantity}")

        except Exception as e:
            logger.error(f"Error executing entry: {e}")

    def execute_exit(self):
        """Execute exit of current position"""
        try:
            if not self.current_position:
                return

            symbol = self.current_position['symbol']
            quantity = self.current_position['quantity']

            # Place sell order
            order_response = self.place_sell_order(symbol, quantity)
            if order_response:
                # Get exit price for logging
                exit_price = self.get_option_price(symbol)
                if exit_price and self.entry_price:
                    pnl = (exit_price - self.entry_price) * quantity
                    logger.info(f"💰 P&L: {pnl:.2f}")

                # Reset position
                self.current_position = None
                self.entry_price = None
                self.stop_loss = None
                self.trailing_active = False

                logger.info(f"✅ Position closed: {symbol}")

        except Exception as e:
            logger.error(f"Error executing exit: {e}")

    def run_strategy(self, max_iterations=None):
        """Main strategy execution loop"""
        logger.info("🚀 Starting EMA Options Buying Strategy")
        logger.info("=" * 60)

        iteration = 0

        try:
            while True:
                iteration += 1

                if max_iterations and iteration > max_iterations:
                    logger.info(f"Reached maximum iterations: {max_iterations}")
                    break

                logger.info(f"📊 Iteration {iteration} - {datetime.now().strftime('%H:%M:%S')}")

                # Check if we have a position
                if self.current_position:
                    # Monitor existing position
                    symbol = self.current_position['symbol']
                    current_price = self.get_option_price(symbol)

                    if current_price:
                        logger.info(f"📈 Current Position: {symbol} @ {current_price:.2f}")
                        logger.info(f"   Entry: {self.entry_price:.2f}, SL: {self.stop_loss:.2f}")

                        # Check exit conditions
                        if self.check_exit_conditions(current_price):
                            self.execute_exit()
                    else:
                        logger.warning("Could not get current option price")

                else:
                    # Look for entry signals
                    signal, df = self.check_entry_signal()
                    if signal:
                        self.execute_entry(signal, df)

                # Wait before next iteration
                time.sleep(30)  # Check every 30 seconds

        except KeyboardInterrupt:
            logger.info("🛑 Strategy stopped by user")
            if self.current_position:
                logger.info("Closing open position...")
                self.execute_exit()
        except Exception as e:
            logger.error(f"Strategy error: {e}")
            if self.current_position:
                logger.info("Closing position due to error...")
                self.execute_exit()

    def get_strategy_status(self):
        """Get current strategy status"""
        status = {
            'timestamp': datetime.now().isoformat(),
            'underlying': self.underlying,
            'timeframe': self.timeframe,
            'position': self.current_position,
            'entry_price': self.entry_price,
            'stop_loss': self.stop_loss,
            'trailing_active': self.trailing_active
        }
        return status

def main():
    """Main function to run the strategy"""
    print("=" * 60)
    print("🎯 EMA Options Buying Strategy")
    print("=" * 60)

    # Get configuration from user
    api_key = input("Enter your OpenAlgo API key: ").strip()
    if not api_key:
        print("❌ API key is required!")
        return

    print("\n📊 Strategy Configuration:")
    underlying = input("Enter underlying (NIFTY/BANKNIFTY) [NIFTY]: ").strip().upper() or "NIFTY"

    print("\n⏰ Available timeframes:")
    print("1. 1m (1 minute)")
    print("2. 2m (2 minutes)")
    print("3. 3m (3 minutes)")
    print("4. 5m (5 minutes)")

    timeframe_choice = input("Choose timeframe (1/2/3/4) [1]: ").strip() or "1"
    timeframe_map = {"1": "1m", "2": "2m", "3": "3m", "4": "5m"}
    timeframe = timeframe_map.get(timeframe_choice, "1m")

    lot_size = int(input("Enter lot size [1]: ").strip() or "1")

    print(f"\n🎯 Strategy Settings:")
    print(f"   Underlying: {underlying}")
    print(f"   Timeframe: {timeframe}")
    print(f"   Lot Size: {lot_size}")
    print(f"   EMA Periods: 9, 21, 34")
    print(f"   MACD: 9, 13")
    print(f"   Stop Loss: 15 points from swing")
    print(f"   Trailing: After 10 points profit")

    confirm = input("\nStart strategy? (y/n): ").strip().lower()
    if confirm != 'y':
        print("Strategy cancelled.")
        return

    # Initialize and run strategy
    try:
        strategy = EMAOptionsStrategy(
            api_key=api_key,
            timeframe=timeframe,
            underlying=underlying,
            lot_size=lot_size
        )

        strategy.run_strategy()

    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
