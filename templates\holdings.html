{% extends 'base.html' %}

{% block content %}
<div class="w-full">
    <!-- Header -->
    <div class="section-header">
        <h1 class="text-2xl font-semibold text-base-content">Investor Summary</h1>
    </div>

    <!-- Holdings Summary Dashboard Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Holding Value Card -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Total Holding Value</div>
                <div class="stat-value text-primary">
                    {{ portfolio_stats.totalholdingvalue| round(2) }}
                </div>
            </div>
        </div>

        <!-- Total Investment Value Card -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Total Investment Value</div>
                <div class="stat-value text-secondary">
                    {{ portfolio_stats.totalinvvalue| round(2) }}
                </div>
            </div>
        </div>

        <!-- Total Profit and Loss Card -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Total Profit and Loss</div>
                <div class="stat-value {% if portfolio_stats.totalprofitandloss > 0 %}text-success{% else %}text-error{% endif %}">
                    {{ portfolio_stats.totalprofitandloss| round(2) }}
                </div>
            </div>
        </div>

        <!-- Total PnL Percentage Card -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Total PnL Percentage</div>
                <div class="stat-value {% if portfolio_stats.totalpnlpercentage > 0 %}text-success{% else %}text-error{% endif %}">
                    {{ portfolio_stats.totalpnlpercentage| round(2) }}%
                </div>
            </div>
        </div>
    </div>

    <!-- Holdings Table -->
    <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
            <div class="overflow-x-auto">
                <table class="table w-full">
                    <thead>
                        <tr>
                            <th>Trading Symbol</th>
                            <th>Exchange</th>
                            <th>Quantity</th>
                            <th>Product</th>
                            <th>Profit and Loss</th>
                            <th>PnL Percentage</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for holding in holdings_data %}
                        <tr class="hover">
                            <td class="font-medium">{{ holding.symbol }}</td>
                            <td>
                                <span class="badge badge-{{ holding.exchange.lower() }}">
                                    {{ holding.exchange }}
                                </span>
                            </td>
                            <td>{{ holding.quantity }}</td>
                            <td>
                                <div class="badge badge-ghost">{{ holding.product }}</div>
                            </td>
                            <td class="{% if holding.pnl > 0 %}text-success{% elif holding.pnl < 0 %}text-error{% endif %}">
                                {{ holding.pnl }}
                            </td>
                            <td class="{% if holding.pnlpercent > 0 %}text-success{% elif holding.pnlpercent < 0 %}text-error{% endif %}">
                                {{ '{:.2f}'.format(holding.pnlpercent) }}%
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
