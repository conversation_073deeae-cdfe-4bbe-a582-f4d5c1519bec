#!/usr/bin/env python3
"""
Options Selling Strategy - CORRECTED VERSION
Strategy: Sell CE/PE when prices drop 10 points below their respective POSP
Trailing SL: 10 points with continuous trailing as price moves favorably
"""

import time
import json
import requests
from datetime import datetime, timedelta
from openalgo import api
import pandas as pd
import threading
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptionsSellingStrategy:
    def __init__(self, api_key, underlying="NIFTY", lot_size=1, points_trigger=10, points_sl=10):
        """
        Initialize Options Selling Strategy

        Args:
            api_key: OpenAlgo API key
            underlying: NIFTY or BANKNIFTY
            lot_size: Number of lots to trade
            points_trigger: Points movement to trigger trade (default: 10)
            points_sl: Stop loss points (default: 10)
        """
        self.api_key = "860d2ab1e890faab82b4d69bd3288845815124dd2d59e9248b3bd20cdd29517a"
        self.underlying = underlying.upper()
        self.lot_size = lot_size
        self.points_trigger = points_trigger
        self.points_sl = points_sl

        # Initialize OpenAlgo client
        self.client = api(api_key=self.api_key, host='http://127.0.0.1:5000')

        # Strategy state - Separate POSP for CE and PE
        self.ce_posp = None  # Point of Start Price for CE
        self.pe_posp = None  # Point of Start Price for PE
        self.ce_position = None  # CE position details
        self.pe_position = None  # PE position details
        self.is_running = False

        # Market hours
        self.market_start = "09:15"
        self.market_end = "15:30"
        self.square_off_time = "15:25"

        # Option symbols
        self.ce_symbol = None
        self.pe_symbol = None
        self.current_expiry = None

    def get_weekly_expiry(self):
        """Get current weekly expiry date in OpenAlgo format"""
        try:
            # Get next Thursday (weekly expiry day for NIFTY)
            today = datetime.now()
            days_ahead = 3 - today.weekday()  # Thursday is 3
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            expiry_date = today + timedelta(days_ahead)

            # Format: DDMMMYY (e.g., 14AUG25)
            return expiry_date.strftime("%d%b%y").upper()
        except Exception as e:
            logger.error(f"Error getting weekly expiry: {e}")
            return None

    def get_spot_price(self):
        """Get current LIVE spot price of the underlying from broker"""
        try:
            # First try to get LIVE price from OpenAlgo/Upstox
            symbol_map = {
                "NIFTY": "NIFTY 50",
                "BANKNIFTY": "NIFTY BANK"
            }

            symbol = symbol_map.get(self.underlying, self.underlying)

            response = requests.post(
                "http://127.0.0.1:5000/api/v1/quotes",
                json={
                    "apikey": self.api_key,
                    "symbol": symbol,
                    "exchange": "NSE"
                }
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    live_price = float(data.get('data', {}).get('ltp', 0))
                    if live_price > 0:
                        logger.info(f"📊 LIVE {self.underlying} price: {live_price}")
                        return live_price

            # Fallback to default values if live price not available
            logger.warning("⚠️ Could not get live price, using fallback values")

            if self.underlying == "NIFTY":
                default_spot = 24300  # Fallback NIFTY level
                logger.info(f"📊 Using fallback NIFTY level: {default_spot}")
            else:  # BANKNIFTY
                default_spot = 52500  # Fallback BANKNIFTY level
                logger.info(f"📊 Using fallback BANKNIFTY level: {default_spot}")
                logger.warning("⚠️ BANKNIFTY options may not be available - recommend using NIFTY")

            return default_spot

        except Exception as e:
            logger.error(f"Error getting spot price: {e}")
            # Return fallback values
            return 24300 if self.underlying == "NIFTY" else 52500

    def get_atm_strike(self, spot_price):
        """Get ATM strike price"""
        try:
            if self.underlying == "NIFTY":
                # Round to nearest 50
                return round(spot_price / 50) * 50
            else:  # BANKNIFTY
                # Round to nearest 100
                return round(spot_price / 100) * 100
        except Exception as e:
            logger.error(f"Error calculating ATM strike: {e}")
            return None

    def get_option_symbols(self, strike_price=None, target_premium=None):
        """Get CE and PE option symbols in OpenAlgo format"""
        try:
            # Get weekly expiry
            if not self.current_expiry:
                self.current_expiry = self.get_weekly_expiry()

            if not self.current_expiry:
                logger.error("Could not determine expiry date")
                return None, None

            # If target premium is specified, find options with premium closest to target
            if target_premium:
                logger.info(f"🎯 Finding options with premium closest to: ₹{target_premium}")
                return self.find_options_by_premium(target_premium)

            # Otherwise use strike-based selection
            if not strike_price:
                spot_price = self.get_spot_price()
                if not spot_price:
                    logger.error("Could not get spot price")
                    return None, None

                # Calculate ATM strike
                strike_price = self.get_atm_strike(spot_price)

            # Format: [UNDERLYING][DDMMMYY][STRIKE][CE/PE]
            # Examples: NIFTY14AUG2524300CE, BANKNIFTY30DEC2552500PE
            self.ce_symbol = f"{self.underlying}{self.current_expiry}{int(strike_price)}CE"
            self.pe_symbol = f"{self.underlying}{self.current_expiry}{int(strike_price)}PE"

            logger.info(f"📋 CE Symbol: {self.ce_symbol}")
            logger.info(f"📋 PE Symbol: {self.pe_symbol}")
            logger.info(f"📊 Strike Price: {strike_price}")
            logger.info(f"📅 Expiry: {self.current_expiry}")

            return self.ce_symbol, self.pe_symbol

        except Exception as e:
            logger.error(f"Error getting option symbols: {e}")
            return None, None

    def find_option_by_premium_smart(self, option_type, target_premium):
        """Smart directional search for single option by premium"""
        try:
            spot_price = self.get_spot_price()
            atm_strike = self.get_atm_strike(spot_price)

            # Strike step based on underlying
            strike_step = 50 if self.underlying == "NIFTY" else 100

            # Start with ATM
            current_strike = atm_strike
            atm_symbol = f"{self.underlying}{self.current_expiry}{int(current_strike)}{option_type}"
            atm_price = self.get_option_price(atm_symbol)

            if not atm_price:
                logger.error(f"Could not get ATM {option_type} price")
                return None, None, None

            logger.info(f"📊 ATM {option_type} ({current_strike}): ₹{atm_price:.2f} | Target: ₹{target_premium}")

            # Determine search direction based on ATM premium vs target
            if atm_price > target_premium:
                # ATM premium is higher than target, move OTM (lower premiums)
                direction = strike_step if option_type == "CE" else -strike_step
                logger.info(f"🔍 ATM > Target, moving OTM (direction: {direction})")
            else:
                # ATM premium is lower than target, move ITM (higher premiums)
                direction = -strike_step if option_type == "CE" else strike_step
                logger.info(f"🔍 ATM < Target, moving ITM (direction: {direction})")

            # Smart search in the right direction
            best_option = (atm_symbol, atm_price, current_strike)
            best_diff = abs(atm_price - target_premium)

            # Search up to 5 strikes in the determined direction
            for i in range(1, 6):
                test_strike = current_strike + (direction * i)
                test_symbol = f"{self.underlying}{self.current_expiry}{int(test_strike)}{option_type}"
                test_price = self.get_option_price(test_symbol)

                if test_price and test_price > 0:
                    test_diff = abs(test_price - target_premium)
                    logger.info(f"🔍 {option_type} {test_strike}: ₹{test_price:.2f} (diff: ₹{test_diff:.2f})")

                    if test_diff < best_diff:
                        best_diff = test_diff
                        best_option = (test_symbol, test_price, test_strike)
                        logger.info(f"🎯 New best {option_type}: ₹{test_price:.2f} (diff: ₹{test_diff:.2f})")
                    else:
                        # If difference is getting worse, we've passed the optimal point
                        logger.info(f"📈 Difference increasing, optimal point found")
                        break
                else:
                    logger.warning(f"❌ No price for {test_symbol}")

                time.sleep(0.1)  # Small delay

            return best_option

        except Exception as e:
            logger.error(f"Error in smart premium search for {option_type}: {e}")
            return None, None, None

    def find_options_by_premium(self, target_premium):
        """Find CE and PE options with premium closest to target - SMART & INDEPENDENT"""
        try:
            if not self.current_expiry:
                logger.error("Expiry not set")
                return None, None

            logger.info(f"🚀 SMART Premium Search: Target ₹{target_premium}")
            logger.info(f"🎯 CE and PE will be selected INDEPENDENTLY with optimal strikes")

            # Find best CE independently
            logger.info(f"\n🔍 Finding Best CE...")
            best_ce = self.find_option_by_premium_smart("CE", target_premium)

            # Find best PE independently
            logger.info(f"\n🔍 Finding Best PE...")
            best_pe = self.find_option_by_premium_smart("PE", target_premium)

            if best_ce and best_pe and best_ce[0] and best_pe[0]:
                self.ce_symbol = best_ce[0]
                self.pe_symbol = best_pe[0]

                logger.info(f"\n✅ SMART INDEPENDENT Selection Complete:")
                logger.info(f"🏆 Best CE: {self.ce_symbol} @ ₹{best_ce[1]:.2f} (Strike: {best_ce[2]})")
                logger.info(f"🏆 Best PE: {self.pe_symbol} @ ₹{best_pe[1]:.2f} (Strike: {best_pe[2]})")
                logger.info(f"💰 Total Premium: ₹{best_ce[1] + best_pe[1]:.2f}")

                # Highlight if strikes are different (which is good!)
                if best_ce[2] != best_pe[2]:
                    logger.info(f"🎯 DIFFERENT STRIKES: CE({best_ce[2]}) ≠ PE({best_pe[2]}) - OPTIMAL!")
                else:
                    logger.info(f"📍 Same strikes: CE({best_ce[2]}) = PE({best_pe[2]})")

                return self.ce_symbol, self.pe_symbol
            else:
                logger.error("❌ Could not find suitable options with target premium")
                return None, None

        except Exception as e:
            logger.error(f"Error finding options by premium: {e}")
            return None, None

    def find_option_by_premium_smart(self, option_type, target_premium):
        """Smart directional search for single option by premium"""
        try:
            spot_price = self.get_spot_price()
            atm_strike = self.get_atm_strike(spot_price)

            # Strike step based on underlying
            strike_step = 50 if self.underlying == "NIFTY" else 100

            # Start with ATM
            current_strike = atm_strike
            atm_symbol = f"{self.underlying}{self.current_expiry}{int(current_strike)}{option_type}"
            atm_price = self.get_option_price(atm_symbol)

            if not atm_price:
                logger.error(f"Could not get ATM {option_type} price")
                return None

            logger.info(f"📊 ATM {option_type} ({current_strike}): ₹{atm_price:.2f} | Target: ₹{target_premium}")

            # Determine search direction based on ATM premium vs target
            if atm_price > target_premium:
                # ATM premium is higher than target, move OTM (lower premiums)
                direction = strike_step if option_type == "CE" else -strike_step
                logger.info(f"🔍 ATM > Target, moving OTM (direction: {direction:+d})")
            else:
                # ATM premium is lower than target, move ITM (higher premiums)
                direction = -strike_step if option_type == "CE" else strike_step
                logger.info(f"🔍 ATM < Target, moving ITM (direction: {direction:+d})")

            # Smart search in the right direction
            best_option = (atm_symbol, atm_price, current_strike)
            best_diff = abs(atm_price - target_premium)

            # Search ALL strikes in the direction to find absolute best (no early stopping)
            all_candidates = [(atm_symbol, atm_price, current_strike)]

            for i in range(1, 12):  # Search up to 12 strikes to be thorough
                test_strike = current_strike + (direction * i)
                test_symbol = f"{self.underlying}{self.current_expiry}{int(test_strike)}{option_type}"
                test_price = self.get_option_price(test_symbol)

                if test_price and test_price > 0:
                    test_diff = abs(test_price - target_premium)
                    all_candidates.append((test_symbol, test_price, test_strike))

                    if test_diff < best_diff:
                        best_diff = test_diff
                        best_option = (test_symbol, test_price, test_strike)
                        logger.info(f"   {i}. {option_type} {test_strike}: ₹{test_price:.2f} (diff: ₹{test_diff:.2f}) 🎯 NEW BEST!")
                    else:
                        logger.info(f"   {i}. {option_type} {test_strike}: ₹{test_price:.2f} (diff: ₹{test_diff:.2f})")
                else:
                    logger.warning(f"   {i}. {option_type} {test_strike}: ❌ No price")
                    # Don't stop on missing prices, continue searching

                time.sleep(0.1)  # Small delay

            # Find absolute best from all candidates
            if all_candidates:
                absolute_best = min(all_candidates, key=lambda x: abs(x[1] - target_premium))
                best_option = absolute_best
                logger.info(f"🔍 Checked {len(all_candidates)} {option_type} options")
                logger.info(f"🏆 ABSOLUTE BEST: {absolute_best[0]} @ ₹{absolute_best[1]:.2f} (Strike: {absolute_best[2]})")

            logger.info(f"🏆 Best {option_type}: {best_option[0]} @ ₹{best_option[1]:.2f} (Strike: {best_option[2]})")
            return best_option

        except Exception as e:
            logger.error(f"Error in smart premium search for {option_type}: {e}")
            return None

    def get_option_price(self, symbol):
        """Get current option price using OpenAlgo quotes API with retry logic"""
        max_retries = 3
        retry_delay = 0.5

        for attempt in range(max_retries):
            try:
                # All option symbols use NFO exchange
                exchange = "NFO"

                response = requests.post(
                    "http://127.0.0.1:5000/api/v1/quotes",
                    json={
                        "apikey": self.api_key,
                        "symbol": symbol,
                        "exchange": exchange
                    },
                    timeout=5  # Add timeout
                )

                if response.status_code == 200:
                    data = response.json()
                    if data.get('status') == 'success':
                        ltp = data.get('data', {}).get('ltp', 0)
                        if ltp and float(ltp) > 0:
                            return float(ltp)
                        else:
                            logger.warning(f"Invalid LTP for {symbol}: {ltp}")
                    else:
                        logger.error(f"Quote API error for {symbol}: {data.get('message', 'Unknown error')}")
                elif response.status_code == 500:
                    logger.warning(f"Server error (500) for {symbol}, attempt {attempt + 1}/{max_retries}")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                else:
                    logger.error(f"HTTP error {response.status_code} getting price for {symbol}")

            except requests.exceptions.Timeout:
                logger.warning(f"Timeout getting price for {symbol}, attempt {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
            except Exception as e:
                logger.error(f"Error getting price for {symbol}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue

        logger.error(f"Failed to get price for {symbol} after {max_retries} attempts")
        return None

    def check_ce_entry_condition(self, current_price):
        """Check if CE sell condition is met"""
        if self.ce_posp is None:
            return False

        # CE sell condition: SELL when CE price drops 10 points BELOW CE_POSP
        return current_price <= (self.ce_posp - self.points_trigger)

    def check_pe_entry_condition(self, current_price):
        """Check if PE sell condition is met"""
        if self.pe_posp is None:
            return False

        # PE sell condition: SELL when PE price drops 10 points BELOW PE_POSP
        return current_price <= (self.pe_posp - self.points_trigger)

    def place_sell_order(self, symbol, option_type):
        """Place sell order for options"""
        try:
            # Calculate lot size based on underlying
            if self.underlying == "NIFTY":
                lot_size = 75  # NIFTY lot size
            else:  # BANKNIFTY
                lot_size = 25  # BANKNIFTY lot size

            quantity = self.lot_size * lot_size

            response = self.client.placeorder(
                strategy=f"Options Selling {option_type}",
                symbol=symbol,
                action="SELL",
                exchange="NFO",
                price_type="MARKET",
                product="MIS",
                quantity=str(quantity)
            )

            logger.info(f"📋 {option_type} SELL Order: {response}")
            return response

        except Exception as e:
            logger.error(f"Error placing {option_type} sell order: {e}")
            return None

    def place_buy_order(self, symbol, option_type):
        """Place buy order to close position"""
        try:
            # Calculate lot size based on underlying
            if self.underlying == "NIFTY":
                lot_size = 75  # NIFTY lot size
            else:  # BANKNIFTY
                lot_size = 25  # BANKNIFTY lot size

            quantity = self.lot_size * lot_size

            response = self.client.placeorder(
                strategy=f"Options Selling {option_type} Exit",
                symbol=symbol,
                action="BUY",
                exchange="NFO",
                price_type="MARKET",
                product="MIS",
                quantity=str(quantity)
            )

            logger.info(f"📋 {option_type} BUY Order (Exit): {response}")
            return response

        except Exception as e:
            logger.error(f"Error placing {option_type} buy order: {e}")
            return None

    def execute_ce_trade(self):
        """Execute CE sell trade"""
        try:
            current_price = self.get_option_price(self.ce_symbol)
            if current_price is None:
                return False

            # Place sell order
            response = self.place_sell_order(self.ce_symbol, "CE")
            if response:
                # Set position details
                self.ce_position = {
                    'symbol': self.ce_symbol,
                    'type': 'CE',
                    'entry_price': current_price,
                    'stop_loss': current_price + self.points_sl,  # SL above entry for short position
                    'order_id': response.get('orderid', 'Unknown')
                }

                logger.info(f"✅ CE Position Opened: Entry {current_price:.2f}, SL {self.ce_position['stop_loss']:.2f}")
                return True

        except Exception as e:
            logger.error(f"Error executing CE trade: {e}")
        return False

    def execute_pe_trade(self):
        """Execute PE sell trade"""
        try:
            current_price = self.get_option_price(self.pe_symbol)
            if current_price is None:
                return False

            # Place sell order
            response = self.place_sell_order(self.pe_symbol, "PE")
            if response:
                # Set position details
                self.pe_position = {
                    'symbol': self.pe_symbol,
                    'type': 'PE',
                    'entry_price': current_price,
                    'stop_loss': current_price + self.points_sl,  # SL above entry for short position
                    'order_id': response.get('orderid', 'Unknown')
                }

                logger.info(f"✅ PE Position Opened: Entry {current_price:.2f}, SL {self.pe_position['stop_loss']:.2f}")
                return True

        except Exception as e:
            logger.error(f"Error executing PE trade: {e}")
        return False

    def update_trailing_sl(self, position, current_price):
        """Update trailing stop loss"""
        try:
            option_type = position['type']

            # For short positions, SL trails DOWN as price moves favorably (down)
            if current_price < position['entry_price']:
                # Price moved favorably (down), trail SL down
                new_sl = current_price + self.points_sl
                if new_sl < position['stop_loss']:
                    position['stop_loss'] = new_sl
                    logger.info(f"📉 {option_type} SL Trailed: {new_sl:.2f} (Price: {current_price:.2f})")

        except Exception as e:
            logger.error(f"Error updating trailing SL: {e}")

    def check_sl_hit(self, position, current_price):
        """Check if stop loss is hit"""
        try:
            # For short positions, SL is hit when price goes ABOVE SL level
            return current_price >= position['stop_loss']
        except Exception as e:
            logger.error(f"Error checking SL: {e}")
            return False

    def exit_position(self, position):
        """Exit a position"""
        try:
            option_type = position['type']
            symbol = position['symbol']

            # Place buy order to close position
            response = self.place_buy_order(symbol, option_type)
            if response:
                logger.info(f"✅ {option_type} Position Closed")

                # Update respective POSP to the current price where position was closed
                current_price = self.get_option_price(symbol)
                if current_price:
                    if option_type == 'CE':
                        self.ce_posp = current_price
                        logger.info(f"📍 New CE POSP set to: {self.ce_posp:.2f}")
                    else:  # PE
                        self.pe_posp = current_price
                        logger.info(f"📍 New PE POSP set to: {self.pe_posp:.2f}")

                return True

        except Exception as e:
            logger.error(f"Error exiting position: {e}")

        return False

    def is_market_hours(self):
        """Check if current time is within market hours"""
        try:
            current_time = datetime.now().time()
            start_time = datetime.strptime(self.market_start, "%H:%M").time()
            end_time = datetime.strptime(self.market_end, "%H:%M").time()

            return start_time <= current_time <= end_time
        except Exception as e:
            logger.error(f"Error checking market hours: {e}")
            return False

    def should_square_off(self):
        """Check if it's time to square off positions"""
        try:
            current_time = datetime.now().time()
            square_off_time = datetime.strptime(self.square_off_time, "%H:%M").time()

            return current_time >= square_off_time
        except Exception as e:
            logger.error(f"Error checking square off time: {e}")
            return False

    def square_off_all_positions(self):
        """Square off all open positions"""
        try:
            if self.ce_position:
                logger.info("🔔 Auto Square Off: Closing CE Position")
                if self.exit_position(self.ce_position):
                    self.ce_position = None

            if self.pe_position:
                logger.info("🔔 Auto Square Off: Closing PE Position")
                if self.exit_position(self.pe_position):
                    self.pe_position = None

        except Exception as e:
            logger.error(f"Error in square off: {e}")

    def display_status(self, ce_price, pe_price):
        """Display current strategy status in clean tabular format"""
        current_time = datetime.now().strftime("%H:%M:%S")

        # Clear screen every 20 updates for cleaner display
        if not hasattr(self, 'update_count'):
            self.update_count = 0
        self.update_count += 1

        if self.update_count % 20 == 1:
            print("\n" + "="*100)
            print("🎯 OPTIONS SELLING STRATEGY - LIVE MONITORING")
            print("="*100)

        # Prepare data
        ce_posp = f"{self.ce_posp:.2f}" if self.ce_posp else "Not Set"
        pe_posp = f"{self.pe_posp:.2f}" if self.pe_posp else "Not Set"

        ce_trigger = f"{self.ce_posp - self.points_trigger:.2f}" if self.ce_posp else "N/A"
        pe_trigger = f"{self.pe_posp - self.points_trigger:.2f}" if self.pe_posp else "N/A"

        # Position status
        ce_status = "⚪ WAITING"
        pe_status = "⚪ WAITING"
        ce_pnl = "N/A"
        pe_pnl = "N/A"
        ce_sl = "N/A"
        pe_sl = "N/A"

        if self.ce_position:
            ce_entry = self.ce_position['entry_price']
            ce_sl = f"{self.ce_position['stop_loss']:.2f}"
            if ce_price is not None:
                ce_pnl_val = (ce_entry - ce_price) * self.lot_size * 50
                ce_pnl = f"₹{ce_pnl_val:.0f}"
            else:
                ce_pnl = "N/A"
            ce_status = "🔴 ACTIVE"

        if self.pe_position:
            pe_entry = self.pe_position['entry_price']
            pe_sl = f"{self.pe_position['stop_loss']:.2f}"
            if pe_price is not None:
                pe_pnl_val = (pe_entry - pe_price) * self.lot_size * 50
                pe_pnl = f"₹{pe_pnl_val:.0f}"
            else:
                pe_pnl = "N/A"
            pe_status = "🔴 ACTIVE"

        # Handle None prices safely
        ce_price_display = f"{ce_price:.2f}" if ce_price is not None else "N/A"
        pe_price_display = f"{pe_price:.2f}" if pe_price is not None else "N/A"

        # Display in clean table format
        print(f"\n⏰ {current_time} | 📊 STRATEGY STATUS")
        print("-" * 100)
        print(f"{'OPTION':<8} {'PRICE':<8} {'POSP':<8} {'TRIGGER':<8} {'STATUS':<12} {'SL':<8} {'P&L':<10}")
        print("-" * 100)
        print(f"{'CE':<8} {ce_price_display:<8} {ce_posp:<8} {ce_trigger:<8} {ce_status:<12} {ce_sl:<8} {ce_pnl:<10}")
        print(f"{'PE':<8} {pe_price_display:<8} {pe_posp:<8} {pe_trigger:<8} {pe_status:<12} {pe_sl:<8} {pe_pnl:<10}")
        print("-" * 100)

        # Show total P&L if any positions
        if self.ce_position or self.pe_position:
            total_pnl = 0
            pnl_available = True

            if self.ce_position and ce_price is not None:
                total_pnl += (self.ce_position['entry_price'] - ce_price) * self.lot_size * 50
            elif self.ce_position:
                pnl_available = False

            if self.pe_position and pe_price is not None:
                total_pnl += (self.pe_position['entry_price'] - pe_price) * self.lot_size * 50
            elif self.pe_position:
                pnl_available = False

            if pnl_available:
                print(f"💰 TOTAL P&L: ₹{total_pnl:.0f}")
            else:
                print(f"💰 TOTAL P&L: N/A (Price data unavailable)")
            print("-" * 100)

    def run_strategy(self):
        """Main strategy execution loop"""
        try:
            logger.info("🚀 Starting Options Selling Strategy")
            logger.info(f"📊 Underlying: {self.underlying}")
            logger.info(f"📦 Lot Size: {self.lot_size}")
            logger.info(f"🎯 Trigger Points: {self.points_trigger}")
            logger.info(f"🛡️ Stop Loss Points: {self.points_sl}")

            # Check if option symbols are already set (from premium selection)
            if not self.ce_symbol or not self.pe_symbol:
                logger.info("📋 Option symbols not set, using default ATM selection")
                ce_symbol, pe_symbol = self.get_option_symbols()
                if not ce_symbol or not pe_symbol:
                    logger.error("❌ Could not get option symbols")
                    return
            else:
                logger.info("✅ Using pre-selected option symbols:")
                logger.info(f"📋 CE: {self.ce_symbol}")
                logger.info(f"📋 PE: {self.pe_symbol}")

            self.is_running = True
            logger.info("✅ Strategy Started - Monitoring prices...")

            while self.is_running:
                try:
                    # Check market hours
                    if not self.is_market_hours():
                        logger.info("⏰ Outside market hours - Strategy paused")
                        time.sleep(60)  # Check every minute
                        continue

                    # Check for square off time
                    if self.should_square_off():
                        logger.info("🔔 Square off time reached")
                        self.square_off_all_positions()
                        break

                    # Get current option prices
                    ce_price = self.get_option_price(self.ce_symbol) if self.ce_symbol else None
                    pe_price = self.get_option_price(self.pe_symbol) if self.pe_symbol else None

                    # Handle price unavailability gracefully
                    if ce_price is None and pe_price is None:
                        logger.warning("⚠️ Both option prices unavailable - waiting...")
                        time.sleep(5)  # Wait longer on errors
                        continue
                    elif ce_price is None:
                        logger.warning(f"⚠️ CE price unavailable for {self.ce_symbol}")
                    elif pe_price is None:
                        logger.warning(f"⚠️ PE price unavailable for {self.pe_symbol}")

                    # Continue with available prices (some operations can work with partial data)

                    # Set initial POSP for each option separately (only if price is available)
                    if self.ce_posp is None and ce_price is not None:
                        self.ce_posp = ce_price  # CE POSP is the initial CE price
                        logger.info(f"📍 CE POSP set to: {self.ce_posp:.2f}")

                    if self.pe_posp is None and pe_price is not None:
                        self.pe_posp = pe_price  # PE POSP is the initial PE price
                        logger.info(f"📍 PE POSP set to: {self.pe_posp:.2f}")

                    # Enhanced POSP Logic: Dynamic tracking when no positions (silent updates)
                    if not self.ce_position and ce_price is not None and self.ce_posp is not None and ce_price > self.ce_posp:
                        self.ce_posp = ce_price

                    if not self.pe_position and pe_price is not None and self.pe_posp is not None and pe_price > self.pe_posp:
                        self.pe_posp = pe_price

                    # Check entry conditions (only if prices are available)
                    ce_entry = self.check_ce_entry_condition(ce_price) if ce_price is not None else False
                    pe_entry = self.check_pe_entry_condition(pe_price) if pe_price is not None else False

                    # Execute trades if conditions are met and no existing position
                    if ce_entry and not self.ce_position:
                        logger.info(f"🔽 CE SELL Condition Met: Price {ce_price:.2f} <= CE_POSP-{self.points_trigger} ({self.ce_posp - self.points_trigger:.2f})")
                        self.execute_ce_trade()

                    if pe_entry and not self.pe_position:
                        logger.info(f"🔽 PE SELL Condition Met: Price {pe_price:.2f} <= PE_POSP-{self.points_trigger} ({self.pe_posp - self.points_trigger:.2f})")
                        self.execute_pe_trade()

                    # Manage existing positions (only if prices are available)
                    if self.ce_position and ce_price is not None:
                        self.update_trailing_sl(self.ce_position, ce_price)
                        if self.check_sl_hit(self.ce_position, ce_price):
                            logger.info(f"🛑 CE Stop Loss Hit at {ce_price:.2f}")
                            if self.exit_position(self.ce_position):
                                # Enhanced POSP Logic: Reset POSP to SL exit price
                                self.ce_posp = ce_price
                                logger.info(f"🔄 CE POSP reset to SL exit price: {self.ce_posp}")
                                self.ce_position = None

                    if self.pe_position and pe_price is not None:
                        self.update_trailing_sl(self.pe_position, pe_price)
                        if self.check_sl_hit(self.pe_position, pe_price):
                            logger.info(f"🛑 PE Stop Loss Hit at {pe_price:.2f}")
                            if self.exit_position(self.pe_position):
                                # Enhanced POSP Logic: Reset POSP to SL exit price
                                self.pe_posp = pe_price
                                logger.info(f"🔄 PE POSP reset to SL exit price: {self.pe_posp}")
                                self.pe_position = None

                    # Display status every 5 seconds for cleaner output
                    if not hasattr(self, 'last_display_time'):
                        self.last_display_time = time.time()

                    current_time = time.time()
                    if current_time - self.last_display_time >= 5:  # Update every 5 seconds
                        self.display_status(ce_price, pe_price)
                        self.last_display_time = current_time

                    # Wait before next iteration (faster polling)
                    time.sleep(0.5)  # 500ms polling for faster response

                except KeyboardInterrupt:
                    logger.info("🛑 Strategy stopped by user")
                    break
                except Exception as e:
                    logger.error(f"Error in strategy loop: {e}")
                    time.sleep(5)

            # Final cleanup
            logger.info("🔄 Strategy stopped - Cleaning up...")
            self.square_off_all_positions()

        except Exception as e:
            logger.error(f"Fatal error in strategy: {e}")
        finally:
            self.is_running = False

def main():
    """Main function"""
    try:
        print("🎯 Options Selling Strategy - CORRECTED VERSION")
        print("=" * 60)
        print("📊 Strategy Logic:")
        print("   • CE SELL: When CE price drops 10 points BELOW CE_POSP")
        print("   • PE SELL: When PE price drops 10 points BELOW PE_POSP")
        print("   • Separate POSP for CE and PE")
        print("   • Trailing SL: 10 points above entry (for short positions)")
        print("=" * 60)

        # Get user preferences
        underlying = input("Enter underlying (NIFTY/BANKNIFTY) [NIFTY]: ").strip().upper() or "NIFTY"
        lot_size = int(input("Enter lot size [1]: ") or "1")

        # Selection method
        print("\n🎯 Option Selection Method:")
        print("1. ATM (At The Money)")
        print("2. Custom Strike Price")
        print("3. Premium-based Selection")

        selection_method = input("Choose method (1/2/3) [1]: ").strip() or "1"

        strike_price = None
        target_premium = None

        if selection_method == "2":
            custom_strike = input("Enter custom strike price: ").strip()
            strike_price = int(custom_strike) if custom_strike else None
        elif selection_method == "3":
            custom_premium = input("Enter target premium (e.g., 200): ").strip()
            target_premium = float(custom_premium) if custom_premium else None

        # Initialize strategy
        api_key = "860d2ab1e890faab82b4d69bd3288845815124dd2d59e9248b3bd20cdd29517a"
        strategy = OptionsSellingStrategy(
            api_key=api_key,
            underlying=underlying,
            lot_size=lot_size
        )

        # Get option symbols based on selection method
        if target_premium:
            logger.info(f"🎯 Using premium-based selection: ₹{target_premium}")
            strategy.get_option_symbols(target_premium=target_premium)
        elif strike_price:
            logger.info(f"📊 Using custom strike: {strike_price}")
            strategy.get_option_symbols(strike_price)
        else:
            logger.info("📊 Using ATM selection")
            strategy.get_option_symbols()

        # Confirm before starting
        print(f"\n🎯 Ready to start strategy:")
        print(f"   Underlying: {underlying}")
        print(f"   Lot Size: {lot_size}")

        if target_premium:
            print(f"   Selection: Premium-based (₹{target_premium})")
        elif strike_price:
            print(f"   Selection: Custom Strike ({strike_price})")
        else:
            print(f"   Selection: ATM")

        confirm = input("\nStart strategy? (y/n): ").strip().lower()
        if confirm == 'y':
            strategy.run_strategy()
        else:
            print("❌ Strategy cancelled")

    except KeyboardInterrupt:
        print("\n🛑 Strategy interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
