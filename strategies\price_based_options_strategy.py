from openalgo import api
import pandas as pd
import numpy as np
import time
import threading
from datetime import datetime, timedelta
import logging
import csv
import os
from price_based_options_config import *

# Configure logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

# Set the API Key
client = api(api_key=API_KEY, host=API_HOST)

class PriceBasedOptionsStrategy:
    def __init__(self, lots=DEFAULT_LOTS):
        self.lots = lots
        self.position = 0  # Current position (0: no position, 1: CE sold, -1: PE sold)
        self.ce_posp = None  # Point of Start Price for CE option
        self.pe_posp = None  # Point of Start Price for PE option
        self.entry_price = None  # Entry price for current position
        self.stop_loss_price = None  # Current stop loss price
        self.current_symbol = None  # Current option symbol being traded
        self.current_strike = None  # Current ATM strike being traded
        self.is_strategy_active = False
        self.last_trade_time = None
        self.trade_count = 0
        self.daily_trades = 0
        self.daily_pnl = 0
        self.last_trade_date = None
        
        # Performance tracking
        self.trade_history = []
        self.performance_data = []
        
        logger.info(f"Strategy initialized with {self.lots} lots")
    
    def get_current_time(self):
        """Get current time in IST"""
        return datetime.now().strftime("%H:%M")
    
    def is_market_open(self):
        """Check if market is open"""
        current_time = self.get_current_time()
        return MARKET_START_TIME <= current_time <= MARKET_END_TIME
    
    def get_atm_strike(self, current_price):
        """Get the nearest ATM strike price"""
        # Round to nearest multiple (50 for NIFTY)
        atm_strike = round(current_price / STRIKE_ROUNDING) * STRIKE_ROUNDING
        return atm_strike
    
    def get_expiry_date(self):
        """Get expiry date based on configuration"""
        current_date = datetime.now()
        
        if EXPIRY_SELECTION == "CURRENT_MONTH":
            if current_date.day > EXPIRY_DAY_THRESHOLD:
                # If after threshold day, use next month
                current_date = current_date.replace(day=1) + timedelta(days=32)
                current_date = current_date.replace(day=1)
        elif EXPIRY_SELECTION == "NEXT_MONTH":
            current_date = current_date.replace(day=1) + timedelta(days=32)
            current_date = current_date.replace(day=1)
        
        expiry_date = current_date.strftime("%d%b%y").upper()
        return expiry_date
    
    def get_option_symbol(self, strike_price, option_type):
        """Generate option symbol based on strike and type"""
        expiry_date = self.get_expiry_date()
        symbol = f"{UNDERLYING_SYMBOL}{expiry_date}{strike_price}{option_type}"
        return symbol
    
    def get_current_price(self, symbol, exchange, retries=MAX_RETRIES):
        """Get current price for a symbol with retries"""
        for attempt in range(retries):
            try:
                # Get real-time quote
                quote = client.quote(symbol=symbol, exchange=exchange)
                if quote and 'ltp' in quote:
                    return float(quote['ltp'])
                else:
                    # Fallback to historical data
                    end_date = datetime.now().strftime("%Y-%m-%d")
                    start_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
                    
                    df = client.history(
                        symbol=symbol,
                        exchange=exchange,
                        interval="1m",
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if not df.empty and 'close' in df.columns:
                        return float(df['close'].iloc[-1])
                    else:
                        logger.error(f"Could not get price for {symbol}")
                        return None
            except Exception as e:
                logger.error(f"Error getting price for {symbol} (attempt {attempt + 1}): {str(e)}")
                if attempt < retries - 1:
                    time.sleep(RETRY_DELAY)
                else:
                    return None
        return None
    
    def initialize_posp(self, nifty_price):
        """Initialize POSP for both CE and PE options based on current ATM strike"""
        try:
            # Get current ATM strike
            atm_strike = self.get_atm_strike(nifty_price)
            self.current_strike = atm_strike
            
            # Get CE and PE symbols for this strike
            ce_symbol = self.get_option_symbol(atm_strike, "CE")
            pe_symbol = self.get_option_symbol(atm_strike, "PE")
            
            # Get current prices for both options
            ce_price = self.get_current_price(ce_symbol, EXCHANGE)
            pe_price = self.get_current_price(pe_symbol, EXCHANGE)
            
            if ce_price is not None and pe_price is not None:
                self.ce_posp = ce_price
                self.pe_posp = pe_price
                logger.info(f"Initialized POSP - CE: {ce_symbol} at {ce_price}, PE: {pe_symbol} at {pe_price}")
                return True
            else:
                logger.error("Could not get option prices for POSP initialization")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing POSP: {str(e)}")
            return False
    
    def place_option_order(self, symbol, action, quantity, price_type="MARKET"):
        """Place option order with retries"""
        for attempt in range(MAX_RETRIES):
            try:
                response = client.placesmartorder(
                    strategy=STRATEGY_NAME,
                    symbol=symbol,
                    action=action,
                    exchange=EXCHANGE,
                    price_type=price_type,
                    product=PRODUCT,
                    quantity=quantity,
                    position_size=self.position
                )
                logger.info(f"Order placed: {action} {quantity} {symbol} - Response: {response}")
                return response
            except Exception as e:
                logger.error(f"Error placing order (attempt {attempt + 1}): {str(e)}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_DELAY)
                else:
                    return None
        return None
    
    def close_all_positions(self):
        """Close all open positions"""
        if self.position != 0:
            try:
                action = "BUY" if self.position > 0 else "SELL"
                response = self.place_option_order(
                    symbol=self.current_symbol,
                    action=action,
                    quantity=abs(self.position)
                )
                if response:
                    logger.info(f"Closed position: {action} {abs(self.position)} {self.current_symbol}")
                    self.position = 0
                    self.entry_price = None
                    self.stop_loss_price = None
                    self.current_symbol = None
            except Exception as e:
                logger.error(f"Error closing positions: {str(e)}")
    
    def update_trailing_stop_loss(self, current_price):
        """Update trailing stop loss"""
        if self.position == 0 or self.entry_price is None:
            return
        
        if self.position > 0:  # CE sold (short position)
            # For CE sell, if price goes down (favorable), move SL down
            new_sl = current_price + TRAILING_SL_POINTS
            if new_sl < self.stop_loss_price:
                self.stop_loss_price = new_sl
                logger.info(f"Updated trailing SL for CE: {self.stop_loss_price}")
        
        elif self.position < 0:  # PE sold (short position)
            # For PE sell, if price goes up (favorable), move SL up
            new_sl = current_price - TRAILING_SL_POINTS
            if new_sl > self.stop_loss_price:
                self.stop_loss_price = new_sl
                logger.info(f"Updated trailing SL for PE: {self.stop_loss_price}")
    
    def check_stop_loss(self, current_price):
        """Check if stop loss is hit"""
        if self.position == 0 or self.stop_loss_price is None:
            return False
        
        if self.position > 0:  # CE sold
            if current_price >= self.stop_loss_price:
                logger.info(f"Stop loss hit for CE at {current_price}")
                return True
        elif self.position < 0:  # PE sold
            if current_price <= self.stop_loss_price:
                logger.info(f"Stop loss hit for PE at {current_price}")
                return True
        
        return False
    
    def check_risk_limits(self):
        """Check if risk limits are exceeded"""
        current_date = datetime.now().date()
        
        # Reset daily counters if new day
        if self.last_trade_date != current_date:
            self.daily_trades = 0
            self.daily_pnl = 0
            self.last_trade_date = current_date
        
        # Check daily trade limit
        if self.daily_trades >= MAX_DAILY_TRADES:
            logger.warning(f"Daily trade limit reached: {self.daily_trades}")
            return False
        
        # Check daily loss limit
        if self.daily_pnl <= -MAX_DAILY_LOSS:
            logger.warning(f"Daily loss limit reached: {self.daily_pnl}")
            return False
        
        return True
    
    def save_trade_data(self, trade_data):
        """Save trade data to CSV file"""
        if SAVE_TRADE_DATA:
            try:
                file_exists = os.path.exists(TRADE_DATA_FILE)
                with open(TRADE_DATA_FILE, 'a', newline='') as file:
                    writer = csv.DictWriter(file, fieldnames=trade_data.keys())
                    if not file_exists:
                        writer.writeheader()
                    writer.writerow(trade_data)
            except Exception as e:
                logger.error(f"Error saving trade data: {str(e)}")
    
    def execute_strategy(self):
        """Main strategy execution loop"""
        logger.info("Starting Price Based Options Strategy...")
        self.is_strategy_active = True
        
        while self.is_strategy_active:
            try:
                # Check if market is open
                if not self.is_market_open():
                    if self.position != 0:
                        logger.info("Market closing - closing all positions")
                        self.close_all_positions()
                    logger.info("Market closed - waiting for next trading day")
                    time.sleep(60)
                    continue
                
                # Check risk limits
                if not self.check_risk_limits():
                    logger.warning("Risk limits exceeded - pausing strategy")
                    time.sleep(300)  # Wait 5 minutes
                    continue
                
                # Get current NIFTY price for ATM strike calculation
                nifty_price = self.get_current_price(UNDERLYING_SYMBOL, "NSE")
                if nifty_price is None:
                    logger.warning("Could not get NIFTY price, retrying...")
                    time.sleep(RETRY_DELAY)
                    continue
                
                # Initialize POSP if not set
                if self.ce_posp is None or self.pe_posp is None:
                    if not self.initialize_posp(nifty_price):
                        logger.warning("Could not initialize POSP, retrying...")
                        time.sleep(RETRY_DELAY)
                        continue
                
                # Check for stop loss
                if self.check_stop_loss(nifty_price):
                    # Close position and set new POSP
                    self.close_all_positions()
                    # Re-initialize POSP with current prices
                    if not self.initialize_posp(nifty_price):
                        logger.warning("Could not re-initialize POSP after stop loss")
                    continue
                
                # Update trailing stop loss if in position
                if self.position != 0:
                    self.update_trailing_stop_loss(nifty_price)
                
                # Check for new trade signals (only if no current position)
                if self.position == 0:
                    # Get current ATM strike and option prices
                    current_atm_strike = self.get_atm_strike(nifty_price)
                    
                    # Check if we need to update POSP for new strike
                    if current_atm_strike != self.current_strike:
                        logger.info(f"ATM strike changed from {self.current_strike} to {current_atm_strike}")
                        if not self.initialize_posp(nifty_price):
                            logger.warning("Could not update POSP for new strike")
                            continue
                    
                    # Get current option prices
                    ce_symbol = self.get_option_symbol(current_atm_strike, "CE")
                    pe_symbol = self.get_option_symbol(current_atm_strike, "PE")
                    
                    ce_price = self.get_current_price(ce_symbol, EXCHANGE)
                    pe_price = self.get_current_price(pe_symbol, EXCHANGE)
                    
                    if ce_price is None or pe_price is None:
                        logger.warning("Could not get option prices, retrying...")
                        time.sleep(RETRY_DELAY)
                        continue
                    
                    # Check for CE sell signal (CE price 10 points below CE POSP)
                    ce_price_diff = ce_price - self.ce_posp
                    if ce_price_diff <= -PRICE_THRESHOLD:
                        # Place CE sell order
                        response = self.place_option_order(ce_symbol, "SELL", self.lots)
                        if response:
                            self.position = self.lots
                            self.entry_price = ce_price
                            self.stop_loss_price = ce_price + STOP_LOSS_POINTS
                            self.current_symbol = ce_symbol
                            self.last_trade_time = datetime.now()
                            self.trade_count += 1
                            self.daily_trades += 1
                            
                            # Save trade data
                            trade_data = {
                                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                'action': 'SELL',
                                'symbol': ce_symbol,
                                'quantity': self.lots,
                                'entry_price': ce_price,
                                'stop_loss': self.stop_loss_price,
                                'ce_posp': self.ce_posp,
                                'pe_posp': self.pe_posp,
                                'nifty_price': nifty_price,
                                'price_diff': ce_price_diff
                            }
                            self.save_trade_data(trade_data)
                            
                            logger.info(f"CE SELL signal executed: {ce_symbol} at {ce_price}, SL: {self.stop_loss_price}, Price diff: {ce_price_diff}")
                    
                    # Check for PE sell signal (PE price 10 points below PE POSP)
                    pe_price_diff = pe_price - self.pe_posp
                    if pe_price_diff <= -PRICE_THRESHOLD:
                        # Place PE sell order
                        response = self.place_option_order(pe_symbol, "SELL", self.lots)
                        if response:
                            self.position = -self.lots
                            self.entry_price = pe_price
                            self.stop_loss_price = pe_price + STOP_LOSS_POINTS
                            self.current_symbol = pe_symbol
                            self.last_trade_time = datetime.now()
                            self.trade_count += 1
                            self.daily_trades += 1
                            
                            # Save trade data
                            trade_data = {
                                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                'action': 'SELL',
                                'symbol': pe_symbol,
                                'quantity': self.lots,
                                'entry_price': pe_price,
                                'stop_loss': self.stop_loss_price,
                                'ce_posp': self.ce_posp,
                                'pe_posp': self.pe_posp,
                                'nifty_price': nifty_price,
                                'price_diff': pe_price_diff
                            }
                            self.save_trade_data(trade_data)
                            
                            logger.info(f"PE SELL signal executed: {pe_symbol} at {pe_price}, SL: {self.stop_loss_price}, Price diff: {pe_price_diff}")
                
                # Log strategy status
                self.log_status(nifty_price, ce_price if 'ce_price' in locals() else None, pe_price if 'pe_price' in locals() else None)
                
                # Wait before next iteration
                time.sleep(TICK_INTERVAL)
                
            except Exception as e:
                logger.error(f"Error in strategy execution: {str(e)}")
                time.sleep(RETRY_DELAY)
                continue
    
    def log_status(self, nifty_price, ce_price=None, pe_price=None):
        """Log current strategy status"""
        status = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "nifty_price": nifty_price,
            "current_strike": self.current_strike,
            "ce_posp": self.ce_posp,
            "pe_posp": self.pe_posp,
            "ce_price": ce_price,
            "pe_price": pe_price,
            "position": self.position,
            "entry_price": self.entry_price,
            "stop_loss": self.stop_loss_price,
            "current_symbol": self.current_symbol,
            "trade_count": self.trade_count,
            "daily_trades": self.daily_trades,
            "daily_pnl": self.daily_pnl
        }
        
        if self.trade_count % LOG_INTERVAL == 0:  # Log every N iterations
            logger.info(f"Strategy Status: {status}")
    
    def stop_strategy(self):
        """Stop the strategy"""
        logger.info("Stopping strategy...")
        self.is_strategy_active = False
        self.close_all_positions()

def main():
    """Main function to run the strategy"""
    print("=== Price Based Options Strategy ===")
    print(f"Underlying: {UNDERLYING_SYMBOL}")
    print(f"Price Threshold: {PRICE_THRESHOLD} points")
    print(f"Stop Loss: {STOP_LOSS_POINTS} points")
    print(f"Trailing SL: {TRAILING_SL_POINTS} points")
    print(f"Market Hours: {MARKET_START_TIME} - {MARKET_END_TIME}")
    print()
    
    # Get user input for lots
    try:
        lots = int(input(f"Enter number of lots (default {DEFAULT_LOTS}): ") or DEFAULT_LOTS)
    except ValueError:
        lots = DEFAULT_LOTS
        print(f"Invalid input, using default: {DEFAULT_LOTS} lots")
    
    strategy_instance = PriceBasedOptionsStrategy(lots=lots)
    
    try:
        strategy_instance.execute_strategy()
    except KeyboardInterrupt:
        logger.info("Strategy interrupted by user")
        strategy_instance.stop_strategy()
    except Exception as e:
        logger.error(f"Strategy error: {str(e)}")
        strategy_instance.stop_strategy()

if __name__ == "__main__":
    main() 