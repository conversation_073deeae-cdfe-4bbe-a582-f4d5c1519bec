# Options Selling Strategy 📈

An automated options selling strategy for NIFTY/BANKNIFTY using Upstox broker integration via OpenAlgo.

## 🎯 Strategy Overview

This strategy implements a **price-movement based options selling approach** with the following logic:

### Core Strategy
- **CE SELL**: When CE price drops 10 points below CE_POSP
- **PE SELL**: When PE price drops 10 points below PE_POSP
- **Separate POSP**: Independent Point of Start Price for CE and PE
- **Trailing SL**: 10 points protection with continuous trailing

### Example Flow
```
CE POSP = 100
Price drops to 90 → SELL CE
Entry Price = 90, Initial SL = 100

If CE price goes to 80:
- Trail SL to 90 (10 points from current favorable price)

If SL hits at 90:
- Exit position
- New CE POSP = 90
- Wait for next 10-point movement from 90
```

## 🚀 Features

- ✅ **Real-time monitoring** of NIFTY options
- ✅ **Separate POSP logic** for CE and PE
- ✅ **Trailing stop loss** management
- ✅ **Market hours** enforcement (9:15 AM - 3:30 PM)
- ✅ **Auto square-off** at 3:25 PM
- ✅ **Full Upstox integration** via OpenAlgo
- ✅ **Comprehensive logging** and monitoring

## 📋 Requirements

### Software
- Python 3.7+
- OpenAlgo platform
- Upstox trading account

### Python Packages
```bash
pip install openalgo requests pandas
```

## 🚀 Usage

### Run the Strategy
```bash
python options_selling_strategy_corrected.py
```

### Test Files
```bash
python test_api_connection.py      # Test API connectivity
python test_corrected_logic.py     # Verify trigger logic
python test_option_symbols.py      # Test symbol generation
```

## 📊 Files Description

| File | Description |
|------|-------------|
| `options_selling_strategy_corrected.py` | Main strategy implementation |
| `test_api_connection.py` | Test OpenAlgo API connectivity |
| `test_corrected_logic.py` | Verify trigger logic |
| `test_option_symbols.py` | Test option symbol generation |

## 🔧 Configuration

### Basic Settings
```python
underlying = "NIFTY"        # NIFTY or BANKNIFTY
lot_size = 1               # Number of lots
points_trigger = 10        # Trigger points for entry
points_sl = 10            # Stop loss points
```

## ⚠️ Risk Disclaimer

- **This is for educational purposes only**
- **Test thoroughly before live trading**
- **Options trading involves significant risk**
- **Use proper risk management**

---

**Happy Trading! 📈🚀**
