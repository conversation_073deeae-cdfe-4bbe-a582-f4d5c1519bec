#!/usr/bin/env python3
"""
Options Selling Strategy - Price Movement Based
Strategy: Sell CE when price drops 10 points, Sell PE when price rises 10 points
Trailing SL: 10 points with continuous trailing as price moves favorably
"""

import time
import json
import requests
from datetime import datetime, timedelta
from openalgo import api
import pandas as pd
import threading
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptionsSellingStrategy:
    def __init__(self, api_key, underlying="NIFTY", lot_size=1, points_trigger=10, points_sl=10):
        """
        Initialize Options Selling Strategy
        
        Args:
            api_key: OpenAlgo API key
            underlying: NIFTY or BANKNIFTY
            lot_size: Number of lots to trade
            points_trigger: Points movement to trigger trade (default: 10)
            points_sl: Stop loss points (default: 10)
        """
        self.api_key = "860d2ab1e890faab82b4d69bd3288845815124dd2d59e9248b3bd20cdd29517a"
        self.underlying = underlying.upper()
        self.lot_size = lot_size
        self.points_trigger = points_trigger
        self.points_sl = points_sl
        
        # Initialize OpenAlgo client
        self.client = api(api_key=self.api_key, host='http://127.0.0.1:5000')
        
        # Strategy state - Separate POSP for CE and PE
        self.ce_posp = None  # Point of Start Price for CE
        self.pe_posp = None  # Point of Start Price for PE
        self.ce_position = None  # CE position details
        self.pe_position = None  # PE position details
        self.is_running = False
        
        # Market hours
        self.market_start = "09:15"
        self.market_end = "15:30"
        self.square_off_time = "15:25"  # Square off 5 minutes before close
        
        # Option selection
        self.ce_symbol = None
        self.pe_symbol = None
        self.current_expiry = None
        
        logger.info(f"🚀 Options Selling Strategy Initialized")
        logger.info(f"📊 Underlying: {self.underlying}")
        logger.info(f"💰 Lot Size: {self.lot_size}")
        logger.info(f"📈 Trigger Points: {self.points_trigger}")
        logger.info(f"🛑 Stop Loss Points: {self.points_sl}")
    
    def get_weekly_expiry(self):
        """Get current weekly expiry date in OpenAlgo format"""
        try:
            # Get next Thursday (weekly expiry day for NIFTY)
            today = datetime.now()
            days_ahead = 3 - today.weekday()  # Thursday is 3
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            expiry_date = today + timedelta(days_ahead)

            # Format: DDMMMYY (e.g., 14AUG25)
            return expiry_date.strftime("%d%b%y").upper()
        except Exception as e:
            logger.error(f"Error getting weekly expiry: {e}")
            return None

    def get_spot_price(self):
        """Get current spot price of the underlying - Using default values since indices not available"""
        try:
            # Since NIFTY/BANKNIFTY indices are not available in OpenAlgo,
            # we'll use current market levels as defaults
            # User can override these with custom strike prices

            if self.underlying == "NIFTY":
                # Current NIFTY levels (update as needed)
                default_spot = 24300  # Approximate current NIFTY level
                logger.info(f"📊 Using default NIFTY level: {default_spot}")
                logger.info("⚠️ NIFTY index not available - using default level")
            else:  # BANKNIFTY
                # Current BANKNIFTY levels (update as needed)
                default_spot = 52500  # Approximate current BANKNIFTY level
                logger.info(f"📊 Using default BANKNIFTY level: {default_spot}")
                logger.info("⚠️ BANKNIFTY index not available - using default level")
                logger.warning("⚠️ BANKNIFTY options may not be available - recommend using NIFTY")

            return default_spot

        except Exception as e:
            logger.error(f"Error getting spot price: {e}")
            return None
    
    def get_atm_strike(self, spot_price):
        """Get ATM strike price"""
        if self.underlying == "NIFTY":
            # NIFTY strikes are in multiples of 50
            return round(spot_price / 50) * 50
        elif self.underlying == "BANKNIFTY":
            # BANKNIFTY strikes are in multiples of 100
            return round(spot_price / 100) * 100
        return spot_price
    
    def get_option_symbols(self, strike_price=None):
        """Get CE and PE option symbols in OpenAlgo format"""
        try:
            # Get weekly expiry
            if not self.current_expiry:
                self.current_expiry = self.get_weekly_expiry()

            if not self.current_expiry:
                logger.error("Could not determine expiry date")
                return None, None

            # Get current spot price if strike not provided
            if not strike_price:
                spot_price = self.get_spot_price()
                if not spot_price:
                    logger.error("Could not get spot price")
                    return None, None

                # Calculate ATM strike
                strike_price = self.get_atm_strike(spot_price)

            # Format: [UNDERLYING][DDMMMYY][STRIKE][CE/PE]
            # Examples: NIFTY14AUG2524300CE, BANKNIFTY30DEC2552500PE
            self.ce_symbol = f"{self.underlying}{self.current_expiry}{int(strike_price)}CE"
            self.pe_symbol = f"{self.underlying}{self.current_expiry}{int(strike_price)}PE"

            logger.info(f"📋 CE Symbol: {self.ce_symbol}")
            logger.info(f"📋 PE Symbol: {self.pe_symbol}")
            logger.info(f"📊 Strike Price: {strike_price}")
            logger.info(f"📅 Expiry: {self.current_expiry}")

            return self.ce_symbol, self.pe_symbol

        except Exception as e:
            logger.error(f"Error getting option symbols: {e}")
            return None, None
    
    def get_option_price(self, symbol):
        """Get current option price using OpenAlgo quotes API"""
        try:
            # All option symbols use NFO exchange
            exchange = "NFO"

            response = requests.post(
                "http://127.0.0.1:5000/api/v1/quotes",
                json={
                    "apikey": self.api_key,
                    "symbol": symbol,
                    "exchange": exchange
                }
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    ltp = data.get('data', {}).get('ltp', 0)
                    logger.info(f"Got price for {symbol}: {ltp}")
                    return float(ltp)
                else:
                    logger.error(f"Quote API error for {symbol}: {data.get('message', 'Unknown error')}")
                    return None
            else:
                logger.error(f"HTTP error {response.status_code} getting price for {symbol}")
                return None

        except Exception as e:
            logger.error(f"Error getting price for {symbol}: {e}")
            return None
    
    def is_market_open(self):
        """Check if market is open"""
        now = datetime.now()
        current_time = now.strftime("%H:%M")
        
        # Check if it's a weekday (Monday=0, Sunday=6)
        if now.weekday() >= 5:  # Saturday or Sunday
            return False
        
        return self.market_start <= current_time <= self.market_end
    
    def should_square_off(self):
        """Check if it's time to square off positions"""
        current_time = datetime.now().strftime("%H:%M")
        return current_time >= self.square_off_time
    
    def place_sell_order(self, symbol, option_type):
        """Place sell order for options"""
        try:
            # Calculate lot size based on underlying
            if self.underlying == "NIFTY":
                lot_size = 75  # NIFTY lot size
            else:  # BANKNIFTY
                lot_size = 25  # BANKNIFTY lot size

            quantity = self.lot_size * lot_size

            response = self.client.placeorder(
                strategy=f"Options Selling {option_type}",
                symbol=symbol,
                action="SELL",
                exchange="NFO",
                price_type="MARKET",
                product="MIS",
                quantity=str(quantity)
            )

            logger.info(f"📋 {option_type} SELL Order: {response}")
            return response

        except Exception as e:
            logger.error(f"Error placing {option_type} sell order: {e}")
            return None
    
    def place_buy_order(self, symbol, option_type):
        """Place buy order to close position"""
        try:
            # Calculate lot size based on underlying
            if self.underlying == "NIFTY":
                lot_size = 75  # NIFTY lot size
            else:  # BANKNIFTY
                lot_size = 25  # BANKNIFTY lot size

            quantity = self.lot_size * lot_size

            response = self.client.placeorder(
                strategy=f"Options Selling {option_type} Exit",
                symbol=symbol,
                action="BUY",
                exchange="NFO",
                price_type="MARKET",
                product="MIS",
                quantity=str(quantity)
            )

            logger.info(f"📋 {option_type} BUY Order (Exit): {response}")
            return response

        except Exception as e:
            logger.error(f"Error placing {option_type} buy order: {e}")
            return None

    def check_ce_entry_condition(self, current_price):
        """Check if CE sell condition is met"""
        if self.ce_posp is None:
            return False

        # CE sell condition: SELL when CE price drops 10 points BELOW CE_POSP
        return current_price <= (self.ce_posp - self.points_trigger)

    def check_pe_entry_condition(self, current_price):
        """Check if PE sell condition is met"""
        if self.pe_posp is None:
            return False

        # PE sell condition: SELL when PE price drops 10 points BELOW PE_POSP
        return current_price <= (self.pe_posp - self.points_trigger)

    def update_trailing_sl(self, position, current_price):
        """Update trailing stop loss"""
        if not position:
            return

        option_type = position['type']
        entry_price = position['entry_price']
        current_sl = position['stop_loss']

        # For both CE and PE sells, we want SL to trail down as option price decreases
        if current_price < entry_price:
            # Price is moving in our favor (option price decreasing)
            new_sl = current_price + self.points_sl

            # Only update if new SL is better (lower) than current SL
            if new_sl < current_sl:
                position['stop_loss'] = new_sl
                logger.info(f"📈 {option_type} Trailing SL updated: {current_sl:.2f} → {new_sl:.2f}")

    def check_sl_hit(self, position, current_price):
        """Check if stop loss is hit"""
        if not position:
            return False

        return current_price >= position['stop_loss']

    def execute_ce_trade(self):
        """Execute CE sell trade"""
        try:
            ce_price = self.get_option_price(self.ce_symbol)
            if ce_price is None:
                return False

            # Place sell order
            response = self.place_sell_order(self.ce_symbol, "CE")
            if response:
                self.ce_position = {
                    'symbol': self.ce_symbol,
                    'type': 'CE',
                    'entry_price': ce_price,
                    'stop_loss': ce_price + self.points_sl,
                    'entry_time': datetime.now(),
                    'order_id': response.get('orderid', '')
                }

                logger.info(f"🔴 CE SELL Executed at {ce_price:.2f}, SL: {self.ce_position['stop_loss']:.2f}")
                return True

        except Exception as e:
            logger.error(f"Error executing CE trade: {e}")

        return False

    def execute_pe_trade(self):
        """Execute PE sell trade"""
        try:
            pe_price = self.get_option_price(self.pe_symbol)
            if pe_price is None:
                return False

            # Place sell order
            response = self.place_sell_order(self.pe_symbol, "PE")
            if response:
                self.pe_position = {
                    'symbol': self.pe_symbol,
                    'type': 'PE',
                    'entry_price': pe_price,
                    'stop_loss': pe_price + self.points_sl,
                    'entry_time': datetime.now(),
                    'order_id': response.get('orderid', '')
                }

                logger.info(f"🔴 PE SELL Executed at {pe_price:.2f}, SL: {self.pe_position['stop_loss']:.2f}")
                return True

        except Exception as e:
            logger.error(f"Error executing PE trade: {e}")

        return False

    def exit_position(self, position):
        """Exit a position"""
        try:
            option_type = position['type']
            symbol = position['symbol']

            # Place buy order to close position
            response = self.place_buy_order(symbol, option_type)
            if response:
                logger.info(f"✅ {option_type} Position Closed")

                # Update respective POSP to the current price where position was closed
                current_price = self.get_option_price(symbol)
                if current_price:
                    if option_type == 'CE':
                        self.ce_posp = current_price
                        logger.info(f"📍 New CE POSP set to: {self.ce_posp:.2f}")
                    else:  # PE
                        self.pe_posp = current_price
                        logger.info(f"📍 New PE POSP set to: {self.pe_posp:.2f}")

                return True

        except Exception as e:
            logger.error(f"Error exiting position: {e}")

        return False

    def square_off_all_positions(self):
        """Square off all open positions before market close"""
        logger.info("🔔 Market closing soon - Squaring off all positions")

        if self.ce_position:
            self.exit_position(self.ce_position)
            self.ce_position = None

        if self.pe_position:
            self.exit_position(self.pe_position)
            self.pe_position = None

    def run_strategy(self):
        """Main strategy execution loop"""
        logger.info("🎯 Starting Options Selling Strategy...")
        logger.info("Press Ctrl+C to stop")

        # Initialize option symbols
        self.get_option_symbols()

        self.is_running = True

        try:
            while self.is_running:
                # Check if market is open
                if not self.is_market_open():
                    logger.info("⏰ Market is closed. Waiting...")
                    time.sleep(60)
                    continue

                # Check if it's time to square off
                if self.should_square_off():
                    self.square_off_all_positions()
                    logger.info("📴 Strategy stopped - Market closing")
                    break

                # Get current option prices
                ce_price = self.get_option_price(self.ce_symbol) if self.ce_symbol else None
                pe_price = self.get_option_price(self.pe_symbol) if self.pe_symbol else None

                if ce_price is None or pe_price is None:
                    logger.warning("⚠️ Unable to get option prices")
                    if ce_price is None:
                        logger.error(f"❌ CE price unavailable for {self.ce_symbol}")
                    if pe_price is None:
                        logger.error(f"❌ PE price unavailable for {self.pe_symbol}")
                    time.sleep(5)  # Wait longer on errors
                    continue

                # Set initial POSP for each option separately
                if self.ce_posp is None:
                    self.ce_posp = ce_price  # CE POSP is the initial CE price
                    logger.info(f"📍 CE POSP set to: {self.ce_posp:.2f}")

                if self.pe_posp is None:
                    self.pe_posp = pe_price  # PE POSP is the initial PE price
                    logger.info(f"📍 PE POSP set to: {self.pe_posp:.2f}")

                # Check entry conditions
                ce_entry = self.check_ce_entry_condition(ce_price)
                pe_entry = self.check_pe_entry_condition(pe_price)

                # Execute trades if conditions are met and no existing position
                if ce_entry and not self.ce_position:
                    logger.info(f"� CE SELL Condition Met: Price {ce_price:.2f} >= CE_POSP+{self.points_trigger} ({self.ce_posp + self.points_trigger:.2f})")
                    self.execute_ce_trade()

                if pe_entry and not self.pe_position:
                    logger.info(f"🔼 PE SELL Condition Met: Price {pe_price:.2f} >= PE_POSP+{self.points_trigger} ({self.pe_posp + self.points_trigger:.2f})")
                    self.execute_pe_trade()

                # Manage existing positions
                if self.ce_position:
                    self.update_trailing_sl(self.ce_position, ce_price)
                    if self.check_sl_hit(self.ce_position, ce_price):
                        logger.info(f"🛑 CE Stop Loss Hit: {ce_price:.2f} >= {self.ce_position['stop_loss']:.2f}")
                        self.exit_position(self.ce_position)
                        self.ce_position = None

                if self.pe_position:
                    self.update_trailing_sl(self.pe_position, pe_price)
                    if self.check_sl_hit(self.pe_position, pe_price):
                        logger.info(f"🛑 PE Stop Loss Hit: {pe_price:.2f} >= {self.pe_position['stop_loss']:.2f}")
                        self.exit_position(self.pe_position)
                        self.pe_position = None

                # Display current status
                self.display_status(ce_price, pe_price)

                # Wait 1 second for next tick
                time.sleep(1)

        except KeyboardInterrupt:
            logger.info("🛑 Strategy stopped by user")
            self.square_off_all_positions()
        except Exception as e:
            logger.error(f"❌ Strategy error: {e}")
        finally:
            self.is_running = False

    def display_status(self, ce_price, pe_price):
        """Display current strategy status"""
        current_time = datetime.now().strftime("%H:%M:%S")

        # Show separate POSP for CE and PE
        ce_posp_str = f"{self.ce_posp:.2f}" if self.ce_posp else "Not Set"
        pe_posp_str = f"{self.pe_posp:.2f}" if self.pe_posp else "Not Set"

        status_msg = f"\n⏰ {current_time} | CE: {ce_price:.2f} (POSP: {ce_posp_str}) | PE: {pe_price:.2f} (POSP: {pe_posp_str})"

        if self.ce_position:
            status_msg += f"\n🔴 CE Position: Entry {self.ce_position['entry_price']:.2f} | SL {self.ce_position['stop_loss']:.2f}"

        if self.pe_position:
            status_msg += f"\n🔴 PE Position: Entry {self.pe_position['entry_price']:.2f} | SL {self.pe_position['stop_loss']:.2f}"

        if not self.ce_position and not self.pe_position:
            # Both CE and PE triggers are BELOW their respective POSP (SELL when prices drop)
            ce_trigger = (self.ce_posp - self.points_trigger) if self.ce_posp else "Not Set"
            pe_trigger = (self.pe_posp - self.points_trigger) if self.pe_posp else "Not Set"

            if isinstance(ce_trigger, float):
                ce_trigger = f"{ce_trigger:.2f}"
            if isinstance(pe_trigger, float):
                pe_trigger = f"{pe_trigger:.2f}"

            status_msg += f"\n⚪ No Positions | CE Trigger: {ce_trigger} | PE Trigger: {pe_trigger}"

        logger.info(status_msg)

    def stop_strategy(self):
        """Stop the strategy"""
        self.is_running = False
        self.square_off_all_positions()

def main():
    """Main function to run the strategy"""
    print("🎯 Options Selling Strategy - Price Movement Based")
    print("=" * 60)

    # API key is hardcoded in the class
    print("✅ Using hardcoded API key")

    print("\nSelect Underlying:")
    print("1. NIFTY")
    print("2. BANKNIFTY")
    choice = input("Enter choice (1 or 2): ").strip()

    underlying = "NIFTY" if choice == "1" else "BANKNIFTY"

    lot_size = input(f"Enter lot size (default: 1): ").strip()
    lot_size = int(lot_size) if lot_size else 1

    points_trigger = input("Enter trigger points (default: 10): ").strip()
    points_trigger = int(points_trigger) if points_trigger else 10

    points_sl = input("Enter stop loss points (default: 10): ").strip()
    points_sl = int(points_sl) if points_sl else 10

    # Option to select specific strike
    print(f"\nOption Strike Selection:")
    print("1. ATM (At The Money)")
    print("2. Custom Strike")
    strike_choice = input("Enter choice (1 or 2): ").strip()

    custom_strike = None
    if strike_choice == "2":
        custom_strike = input("Enter strike price: ").strip()
        custom_strike = int(custom_strike) if custom_strike else None

    print(f"\n📋 Configuration:")
    print(f"   Underlying: {underlying}")
    print(f"   Lot Size: {lot_size}")
    print(f"   Trigger Points: {points_trigger}")
    print(f"   Stop Loss Points: {points_sl}")
    if custom_strike:
        print(f"   Custom Strike: {custom_strike}")

    confirm = input("\nStart strategy? (y/n): ").strip().lower()
    if confirm != 'y':
        print("Strategy cancelled.")
        return

    # Initialize and run strategy
    try:
        strategy = OptionsSellingStrategy(
            api_key="860d2ab1e890faab82b4d69bd3288845815124dd2d59e9248b3bd20cdd29517a",
            underlying=underlying,
            lot_size=lot_size,
            points_trigger=points_trigger,
            points_sl=points_sl
        )

        # Set custom strike if provided
        if custom_strike:
            strategy.get_option_symbols(strike_price=custom_strike)

        strategy.run_strategy()

    except Exception as e:
        logger.error(f"Failed to start strategy: {e}")

if __name__ == "__main__":
    main()
