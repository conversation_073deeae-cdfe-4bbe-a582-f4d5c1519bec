#!/usr/bin/env python3
"""
Configuration file for EMA Options Buying Strategy
"""

# Strategy Parameters
STRATEGY_CONFIG = {
    # Basic Settings
    "STRATEGY_NAME": "EMA_OPTIONS_BUYING",
    "VERSION": "1.0",
    
    # Technical Indicators
    "EMA_PERIODS": [9, 21, 34],
    "MACD_FAST": 9,
    "MACD_SLOW": 13,
    "MACD_SIGNAL": 9,
    
    # Risk Management
    "STOP_LOSS_POINTS": 15,
    "TRAILING_TRIGGER_POINTS": 10,
    "MAX_LOSS_PERCENT": 70,  # Maximum loss as % of entry price
    
    # Position Management
    "DEFAULT_LOT_SIZE": 1,
    "MAX_POSITIONS": 1,  # Only one position at a time
    
    # Timeframes
    "AVAILABLE_TIMEFRAMES": ["1m", "2m", "3m", "5m"],
    "DEFAULT_TIMEFRAME": "1m",
    
    # Option Selection
    "OPTION_SELECTION_MODES": ["ATM", "ITM"],
    "DEFAULT_SELECTION": "ATM",
    
    # Underlying Assets
    "SUPPORTED_UNDERLYINGS": ["NIFTY", "BANKNIFTY"],
    "DEFAULT_UNDERLYING": "NIFTY",
    
    # Lot Multipliers
    "LOT_MULTIPLIERS": {
        "NIFTY": 50,
        "BANKNIFTY": 25
    },
    
    # Strike Intervals
    "STRIKE_INTERVALS": {
        "NIFTY": 50,
        "BANKNIFTY": 100
    },
    
    # Fallback Prices (when live data unavailable)
    "FALLBACK_PRICES": {
        "NIFTY": 24500,
        "BANKNIFTY": 51000
    },
    
    # Timing
    "ITERATION_DELAY": 30,  # seconds between strategy checks
    "API_TIMEOUT": 10,      # seconds for API calls
    "QUOTE_TIMEOUT": 5,     # seconds for quote requests
    
    # Logging
    "LOG_LEVEL": "INFO",
    "LOG_FILE": "ema_options_strategy.log",
    "LOG_FORMAT": "%(asctime)s - %(levelname)s - %(message)s",
    
    # Data Requirements
    "MIN_CANDLES_REQUIRED": 50,
    "LOOKBACK_PERIODS": 100,
    "SWING_LOOKBACK": 10,
    
    # Market Hours (IST)
    "MARKET_START": "09:15",
    "MARKET_END": "15:30",
    
    # OpenAlgo Settings
    "OPENALGO_HOST": "http://127.0.0.1:5000",
    "EXCHANGE_SPOT": "NSE",
    "EXCHANGE_OPTIONS": "NFO",
    "PRODUCT_TYPE": "MIS",
    "PRICE_TYPE": "MARKET"
}

# Symbol Mappings
SYMBOL_MAPPINGS = {
    "NIFTY": "NIFTY 50",
    "BANKNIFTY": "NIFTY BANK"
}

# Validation Rules
VALIDATION_RULES = {
    "API_KEY_LENGTH": 64,  # Expected length of API key
    "MIN_LOT_SIZE": 1,
    "MAX_LOT_SIZE": 10,
    "MIN_STOP_LOSS": 5,
    "MAX_STOP_LOSS": 50,
    "MIN_TRAILING_TRIGGER": 5,
    "MAX_TRAILING_TRIGGER": 30
}

def validate_config(config):
    """Validate strategy configuration"""
    errors = []
    
    # Check required fields
    required_fields = [
        "EMA_PERIODS", "MACD_FAST", "MACD_SLOW", 
        "STOP_LOSS_POINTS", "TRAILING_TRIGGER_POINTS"
    ]
    
    for field in required_fields:
        if field not in config:
            errors.append(f"Missing required field: {field}")
    
    # Validate EMA periods
    if "EMA_PERIODS" in config:
        if len(config["EMA_PERIODS"]) != 3:
            errors.append("EMA_PERIODS must contain exactly 3 periods")
        if not all(isinstance(p, int) and p > 0 for p in config["EMA_PERIODS"]):
            errors.append("EMA_PERIODS must be positive integers")
    
    # Validate MACD settings
    if "MACD_FAST" in config and "MACD_SLOW" in config:
        if config["MACD_FAST"] >= config["MACD_SLOW"]:
            errors.append("MACD_FAST must be less than MACD_SLOW")
    
    # Validate stop loss
    if "STOP_LOSS_POINTS" in config:
        sl = config["STOP_LOSS_POINTS"]
        if not (VALIDATION_RULES["MIN_STOP_LOSS"] <= sl <= VALIDATION_RULES["MAX_STOP_LOSS"]):
            errors.append(f"STOP_LOSS_POINTS must be between {VALIDATION_RULES['MIN_STOP_LOSS']} and {VALIDATION_RULES['MAX_STOP_LOSS']}")
    
    return errors

def get_config():
    """Get validated strategy configuration"""
    config = STRATEGY_CONFIG.copy()
    
    # Validate configuration
    errors = validate_config(config)
    if errors:
        raise ValueError(f"Configuration errors: {', '.join(errors)}")
    
    return config

def get_symbol_mapping(underlying):
    """Get symbol mapping for underlying"""
    return SYMBOL_MAPPINGS.get(underlying, underlying)

def get_lot_multiplier(underlying):
    """Get lot multiplier for underlying"""
    return STRATEGY_CONFIG["LOT_MULTIPLIERS"].get(underlying, 50)

def get_strike_interval(underlying):
    """Get strike interval for underlying"""
    return STRATEGY_CONFIG["STRIKE_INTERVALS"].get(underlying, 50)

def get_fallback_price(underlying):
    """Get fallback price for underlying"""
    return STRATEGY_CONFIG["FALLBACK_PRICES"].get(underlying, 24500)

# Export main configuration
__all__ = [
    'STRATEGY_CONFIG', 
    'SYMBOL_MAPPINGS', 
    'VALIDATION_RULES',
    'get_config',
    'get_symbol_mapping',
    'get_lot_multiplier', 
    'get_strike_interval',
    'get_fallback_price',
    'validate_config'
]
